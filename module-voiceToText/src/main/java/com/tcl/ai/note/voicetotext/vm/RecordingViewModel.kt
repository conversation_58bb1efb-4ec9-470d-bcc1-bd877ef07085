package com.tcl.ai.note.voicetotext.vm

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.event.UiSateEvent
import com.tcl.ai.note.utils.AppActivityManager
import com.tcl.ai.note.voicetotext.audio.ShowAudioRecorder
import com.tcl.ai.note.voicetotext.util.AudioToTextConstant
import com.tcl.ai.note.voicetotext.data.AudioRepository
import com.tcl.ai.note.voicetotext.intent.RecordIntent
import com.tcl.ai.note.voicetotext.states.RecordSpecialState
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.voicetotext.audio.RecordingForegroundService
import com.tcl.ai.note.voicetotext.util.deleteFile
import com.tcl.ai.note.voicetotext.util.generateAudioPath
import com.tcl.ai.note.voicetotext.util.extractNoteIdFromAudioPath
import com.tcl.ai.note.voicetotext.states.RecordingState
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class RecordingViewModel @Inject constructor(@ApplicationContext val context: Context, private val repository: AudioRepository) : ViewModel() {

    private val _recordState = MutableStateFlow(RecordingState())
    private val audiosSize = MutableStateFlow(0)
    val _isShowBottomAIPop = MutableStateFlow(false)
    val recordState = _recordState.asStateFlow()

    init {
        // 设置为测试模式，方便测试
//        AudioToTextConstant.useTestMode()
        // 在生产环境中应该使用
         AudioToTextConstant.useProductionMode()
        Logger.d(TAG, "Using test mode for recording time configuration")

        observeRecordingState()
        viewModelScope.launch {
            AppActivityManager.isLeaveHintStateFlow.collect {
                Logger.d(TAG, "isLeaveHintStateFlow: $it, isAddingImage: ${AppActivityManager.isAddingImage.value}")
                Logger.d(TAG, "isLeaveHintStateFlow: $it, isTakingPhoto: ${AppActivityManager.isTakingPhoto.value}")
                if (it && !AppActivityManager.isAddingImage.value && !AppActivityManager.isTakingPhoto.value) {
                    Logger.d(TAG, "AppActivity is leave hint, stopRecord")
                    if (!_isShowBottomAIPop.value && _recordState.value.isRecording) {
                        Logger.d(TAG, "stopRecord")
                        stopRecord()
                    }
                }
            }
        }
        observeUiStateChanges()
    }

    private fun observeUiStateChanges() {
        viewModelScope.launch {
            UiSateEvent.uiStateChangeEvent.collect { size ->
                audiosSize.value = size
            }
        }
    }

    fun recordingIntent(intent: RecordIntent) {
        if (audiosSize.value > 99) {
            ToastUtils.makeWithCancel(com.tcl.ai.note.base.R.string.recording_over_limit)
            return
        }
        when (intent) {
            is RecordIntent.StartRecord -> {
                Logger.d(TAG, "StartRecord")
                startRecording(intent.audioPath)
            }
            is RecordIntent.SaveRecord -> {
                Logger.d(TAG, "SaveRecord isRecording: ${ShowAudioRecorder.isRecording}")
                if (ShowAudioRecorder.isRecording) {
                    val duration = ShowAudioRecorder.stopRecord()
                    if (duration > AudioToTextConstant.MIN_RECORD_DURATION) {
                        updateRecordingState(RecordingState(isRecording = false,
                            recordingIconVisible = false, recordDuration = duration, audioPath = ShowAudioRecorder.audioFilePath))
                    } else {
                        deleteAudioFile(ShowAudioRecorder.audioFilePath)
                        updateRecordingState(RecordingState(isRecording = false, audioPath = ShowAudioRecorder.audioFilePath,
                            specialState = RecordSpecialState.RecordingError(context.getString(com.tcl.ai.note.base.R.string.edit_audio_record_shortest_time))))
                    }
                }
            }
            is RecordIntent.StopRecord -> {
                Logger.d(TAG, "StopRecord")
                stopRecord()
            }
        }
    }

    private fun observeRecordingState() {
        viewModelScope.launch {
            ShowAudioRecorder.recordingState.collect {
                it.specialState?.let { specialState ->
                    when (specialState) {
                        is RecordSpecialState.RecordingError -> {
                            deleteAudioFile(it.audioPath)
                        }

                        RecordSpecialState.AudioFocusLost -> {
                            Logger.d(TAG, "AudioFocusLost, stopRecord")
                            stopRecord()
                        }

                        RecordSpecialState.MaxDurationReached -> {
                            Logger.d(TAG, "MaxDurationReached detected! Starting auto save and new recording")
                            // 当录音达到最大时长时，自动保存当前录音并开始新录音
                            autoSaveAndStartNewRecording()
                        }

                        RecordSpecialState.SaveAndStartNew -> {
                            Logger.d(TAG, "SaveAndStartNew state detected")
                            // 对于"保存并开始新录音"状态，不需要特殊处理
                            // 状态会在UI层面被正确处理
                        }
                    }
                }
                updateRecordingState(it)
            }
        }
    }

    private fun startRecording(audioPath: String) {
        if (!ShowAudioRecorder.isRecording) {
            viewModelScope.launch {
                //ShowAudioRecorder.startRecord()
                RecordingForegroundService.startServiceForStartRecording(audioPath)
            }
        }
    }

    fun stopRecord() {
        Logger.d(TAG, "stopRecord")
        recordingIntent(RecordIntent.SaveRecord)
        RecordingForegroundService.stopRecordingService()
    }

    private fun updateRecordingState(recordingState: RecordingState) {
        _recordState.value = recordingState
    }

    fun resetRecordingState() {
        // 重置 ShowAudioRecorder 的状态
        ShowAudioRecorder.resetRecordingState()
        // 重置 RecordingViewModel 的状态
        updateRecordingState(RecordingState())
    }

    /**
     * 更新录音状态为"保存并开始新录音"操作
     */
    fun updateRecordingStateForSaveAndStartNew(audioPath: String?, duration: Long) {
        if (duration > AudioToTextConstant.MIN_RECORD_DURATION) {
            updateRecordingState(RecordingState(
                isRecording = false,
                recordingIconVisible = false,
                recordDuration = duration,
                audioPath = audioPath,
                specialState = RecordSpecialState.SaveAndStartNew
            ))
        } else {
            deleteAudioFile(audioPath)
            updateRecordingState(RecordingState(
                isRecording = false,
                audioPath = audioPath,
                specialState = RecordSpecialState.RecordingError(context.getString(com.tcl.ai.note.base.R.string.edit_audio_record_shortest_time))
            ))
        }
    }

    /**
     * 自动保存当前录音并开始新录音（当达到最大时长时）
     */
    private fun autoSaveAndStartNewRecording() {
        // 获取当前录音状态
        val currentState = ShowAudioRecorder.recordingState.value
        val currentAudioPath = currentState.audioPath
        val duration = currentState.recordDuration

        Logger.d(TAG, "autoSaveAndStartNewRecording: currentAudioPath = $currentAudioPath, duration = $duration")

        // 从当前录音路径中提取noteId
        val noteId = extractNoteIdFromAudioPath(currentAudioPath)
        Logger.d(TAG, "autoSaveAndStartNewRecording: extracted noteId = $noteId")

        if (noteId == 0L) {
            Logger.e(TAG, "autoSaveAndStartNewRecording: failed to extract noteId from path: $currentAudioPath")
            return
        }

        // 完全释放录音资源
        Logger.d(TAG, "autoSaveAndStartNewRecording: releasing recording resources")
        ShowAudioRecorder.releaseRecordingResources()

        // 录音已经在updateMicStatus中被停止了，这里只需要处理保存逻辑
        if (duration > AudioToTextConstant.MIN_RECORD_DURATION) {
            // 保存当前录音 - 使用特殊状态标记这是自动保存的录音
            updateRecordingState(RecordingState(
                isRecording = false,
                recordingIconVisible = false,
                recordDuration = duration,
                audioPath = currentAudioPath,
                specialState = RecordSpecialState.MaxDurationReached // 标记为最大时长自动保存
            ))
            Logger.d(TAG, "autoSaveAndStartNewRecording: saved recording with duration $duration")
        } else {
            // 录音时间太短，删除文件
            deleteAudioFile(currentAudioPath)
            Logger.d(TAG, "autoSaveAndStartNewRecording: recording too short, deleted file")
        }

        // 停止录音服务
        RecordingForegroundService.stopRecordingService()

        // 延迟一段时间后自动开始新录音
        viewModelScope.launch {
            kotlinx.coroutines.delay(1000) // 增加延迟时间确保旧录音完全停止

            // 重置录音状态，清除MaxDurationReached状态
            resetRecordingState()

            val newAudioPath = generateAudioPath(noteId)
            Logger.d(TAG, "autoSaveAndStartNewRecording: starting new recording with path = $newAudioPath")
            recordingIntent(RecordIntent.StartRecord(newAudioPath))
        }
    }

    fun deleteAudioFile(audioPath: String?) {
        viewModelScope.launchIO {
            deleteFile(audioPath)
            audioPath?.let {
                repository.deleteTransfersByAudioPath(audioPath)
            }
        }
    }

    companion object {
        private const val TAG = "RecordingViewModel"
    }

    /**
     * 当ViewModel被清除时调用，例如在应用被杀掉时
     * 确保停止所有正在进行的录音
     */
    override fun onCleared() {
        super.onCleared()
        Logger.d(TAG, "onCleared: 停止录音状态")

//        // 停止录音服务
        if (ShowAudioRecorder.isRecording) {
            stopRecord()
        }
    }
}