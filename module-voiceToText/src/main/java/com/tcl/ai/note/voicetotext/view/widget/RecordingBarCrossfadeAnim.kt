package com.tcl.ai.note.voicetotext.view.widget

import androidx.compose.animation.Crossfade
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.runtime.Composable
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.voicetotext.R
import com.tcl.ai.note.voicetotext.states.RecordSpecialState
import com.tcl.ai.note.voicetotext.states.RecordingState
import com.tcl.ai.note.voicetotext.util.AudioToTextConstant
import com.tcl.ai.note.voicetotext.vm.RecordingViewModel
import com.tcl.ai.note.widget.DelayedBackgroundIconButton

@Composable
fun RecordingBarCrossfadeAnim(
    noteId: Long,
    audioPath: String?,
    hasAudio: Boolean,
    showAudioPanel: Boolean,
    onStopAudioClick: () -> Unit,
    onRecordingError: (String?) -> Unit,
    onAddAudio: (String) -> Unit,
    onAudioPanelVisibleClick: () -> Unit,
    recordingViewModel: RecordingViewModel = hiltViewModel(),
) {
    val recordingState by recordingViewModel.recordState.collectAsState()
    val dimens = getGlobalDimens()

    // 录音时使用自适应宽度，非录音时使用固定宽度并保持动画效果
    val targetWidth = if (recordingState.isRecording) Dp.Unspecified else 40.dp
    
    LaunchedEffect(recordingState) {
        handleRecordingState(audioPath, recordingState, onRecordingError = onRecordingError, onStopAudioClick = onStopAudioClick, recordingViewModel)
    }

    Box(modifier = Modifier
        .run {
            if (recordingState.isRecording) {
                wrapContentWidth()
            } else {
                val animatedWidth by animateDpAsState(targetWidth, label = "recordBarWidth")
                width(animatedWidth)
            }
        }
        .animateContentSize() // 添加内容尺寸变化动画
        .height(32.dp),
        contentAlignment = Alignment.Center) {

        Box(
            modifier = Modifier.height(dimens.btnSize),
            contentAlignment = Alignment.CenterStart
        ) {
            Crossfade(
                targetState = recordingState.isRecording,
                label = "recordBarCrossFade"
            ) { expanded ->
                if (expanded) {
                    TitleAudioBlock(
                        noteId = noteId,
                        audioPath = audioPath,
                        onStopRecordClick = { path ->
                            recordingViewModel.stopRecord()
                            onStopAudioClick()
                        },
                        onRecordingError = {
                            recordingViewModel.deleteAudioFile(recordingState.audioPath)
                            onRecordingError(recordingState.audioPath ?: "")
                        },
                        onAddAudio = onAddAudio,
                        recordingViewModel = recordingViewModel,
                    )
                } else {
                    if (hasAudio) {
                        DelayedBackgroundIconButton(
                            btnSize = dimens.btnSize,
                            iconSize = TclTheme.dimens.iconSize,
                            onClick = {
                                onAudioPanelVisibleClick()
                            },
                            painter = painterResource(com.tcl.ai.note.base.R.drawable.icon_audio_panel_visible),
                            isChecked = showAudioPanel,
                            contentDescription = if (showAudioPanel) {
                                com.tcl.ai.note.base.R.string.accessibility_hide_audio_panel.stringRes()
                            } else {
                                com.tcl.ai.note.base.R.string.accessibility_show_audio_panel.stringRes()
                            },
                        )
                    }
                }
            }
        }
    }
}

private fun handleRecordingState(
    audioPath: String?,
    recordingState: RecordingState,
    onRecordingError: (String?) -> Unit,
    onStopAudioClick: () -> Unit,
    recordingViewModel: RecordingViewModel
) {
    if (audioPath == recordingState.audioPath) {
        when (recordingState.specialState) {
            is RecordSpecialState.RecordingError -> {
                ToastUtils.makeWithCancel(recordingState.specialState.message)
                onRecordingError(recordingState.audioPath)
                recordingViewModel.deleteAudioFile(recordingState.audioPath)
                recordingViewModel.resetRecordingState()
            }
            is RecordSpecialState.SaveAndStartNew -> {
                // 对于"保存并开始新录音"操作，不触发正常的停止回调
                // 这样可以避免重复添加录音到列表
                recordingViewModel.resetRecordingState()
            }
            is RecordSpecialState.MaxDurationReached -> {
                // 对于"达到最大时长自动保存"操作，触发停止回调以添加录音到列表
                onStopAudioClick()
                // 这里不调用resetRecordingState()，让autoSaveAndStartNewRecording方法完成后再重置
            }
            else -> {
                // 处理正常的录音完成情况
                if (!recordingState.isRecording && recordingState.recordDuration > 0 && recordingState.recordDuration < AudioToTextConstant.MIN_RECORD_DURATION) {
                    ToastUtils.makeWithCancel(com.tcl.ai.note.base.R.string.edit_audio_record_shortest_time)
                    onRecordingError(recordingState.audioPath)
                    recordingViewModel.deleteAudioFile(recordingState.audioPath)
                    recordingViewModel.resetRecordingState()
                }
            }
        }
    }
}