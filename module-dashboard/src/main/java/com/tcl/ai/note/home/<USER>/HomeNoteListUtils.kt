package com.tcl.ai.note.home.utils

import android.text.format.DateFormat
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.FirstScreenThumbnailType
import com.tcl.ai.note.handwritingtext.database.entity.NoteListItem
import com.tcl.ai.note.handwritingtext.repo.HandWritingThumbnailRepo
import com.tcl.ai.note.handwritingtext.richtext.converter.offset
import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.handwritingtext.vm.text.converter.RichTextBlockConverter
import com.tcl.ai.note.home.model.HomeNoteItemModel
import com.tcl.ai.note.home.model.NoteDisplayInfo
import com.tcl.ai.note.home.model.ThumbnailInfo
import com.tcl.ai.note.home.model.ThumbnailType
import com.tcl.ai.note.utils.Logger
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Locale
import kotlin.system.measureTimeMillis


/**
 * 检测系统是否使用24小时制
 */
private fun is24HourFormat(): Boolean {
    return DateFormat.is24HourFormat(GlobalContext.appContext)
}

/**
 * 获取时间格式化器，根据系统设置动态选择24小时制或12小时制
 * 注意：这个函数用于非Compose环境，在Compose中请使用rememberTimeFormatter()
 */
private fun getTimeFormatter(): DateTimeFormatter {
    val currentLocale = Locale.getDefault()
    val pattern = if (is24HourFormat()) {
        "HH:mm"  // 24小时制
    } else {
        // 12小时制，根据语言环境选择AM/PM格式
        if (currentLocale.language == "zh") {
            "a h:mm"  // 中文：上午/下午 7:00
        } else {
            "h:mm a"  // 英文：7:00 AM/PM
        }
    }
    return DateTimeFormatter.ofPattern(pattern).withLocale(currentLocale)
}

private val dateFormatterZh by lazy {
    DateTimeFormatter.ofPattern("yyyy年MM月dd日").withLocale(Locale.getDefault())
}

private val dateFormatterEn by lazy {
    DateTimeFormatter.ofPattern("MMM dd, yyyy").withLocale(Locale.getDefault())
}

// 缓存今天的日期，避免重复计算
private var cachedToday: LocalDate? = null
private var lastDateCheck: Long = 0L

private fun getTodayDate(): LocalDate {
    val now = System.currentTimeMillis()
    // 每小时更新一次今天的日期缓存
    if (cachedToday == null || now - lastDateCheck > 3600000) {
        cachedToday = LocalDate.now()
        lastDateCheck = now
    }
    return cachedToday!!
}

/**
 * 格式化日期 - 优化版本
 */
fun formatDate(createTime: Long?, modifyTime: Long?, isCreateTimeSort: Boolean): String {
    val time = if (isCreateTimeSort) createTime else modifyTime ?: 0L

    if (time == 0L || time == null) return ""

    // 将时间戳转换为本地时区的日期时间
    val zonedDateTime = Instant.ofEpochMilli(time).atZone(ZoneId.systemDefault())
    val noteDate = zonedDateTime.toLocalDate()
    val today = getTodayDate()

    // 如果是今天，显示时间（根据系统设置选择24小时制或12小时制）
    if (noteDate.isEqual(today)) {
        return zonedDateTime.format(getTimeFormatter())
    }

    // 如果不是今天，显示完整日期
    val currentLocale = Locale.getDefault()
    val formatter = if (currentLocale.language == "zh") dateFormatterZh else dateFormatterEn
    return zonedDateTime.format(formatter)
}


/**
 * 是否是全部和未分类
 */
fun isAllAndUnCategorised(selectedCategoryId:String): Boolean {
    return (selectedCategoryId.isEmpty()||selectedCategoryId==TYPE_UN_CATEGORISED_ID.toString())
}
/**
 * 将数据库实体转换为UI数据模型
 * @param notes 数据库笔记列表
 * @param isCreateTimeSort 是否按创建时间排序（用于日期显示格式）
 */
internal fun mapToHomeNoteItems(
    notes: List<NoteListItem>,
    isCreateTimeSort: Boolean = true
): List<HomeNoteItemModel> {
    val result: List<HomeNoteItemModel>
    val timeMillis = measureTimeMillis {

        result = notes.map { original ->
            // 数据库里的 andwritingThumbnail  现在是缩略图类型 FirstScreenThumbnailType
            val oldHandwritingThumbnail1 = original.handwritingThumbnail
            val firstScreenThumbnailType = FirstScreenThumbnailType.fromDBString(oldHandwritingThumbnail1)
            // 本地文件为准
            ///data/data/com.tcl.ai.note.hd/files/HandWritingThumbnails/handwriting_thumbnails_noteId_240.png
            //data/data/com.tcl.ai.note.hd/files/HandWritingThumbnails/handwriting_thumbnails_noteId_240_dark.png
//            val handwritingThumbnail = note.handwritingThumbnail
//            val handwritingThumbnailDark = handwritingThumbnail?.replace(".png", "_dark.png")
            val handwritingThumbnail = HandWritingThumbnailRepo.getBitmapPath(original.noteId)
            val handwritingThumbnailDark = HandWritingThumbnailRepo.getBitmapPath(original.noteId, true)
            val bitmapLastModifyMillis =
                HandWritingThumbnailRepo.getBitmapLastModifyMillis(original.noteId)
            val newNote=original.copy(handwritingThumbnail = handwritingThumbnail)
            val categoryIdStr = newNote.categoryId.toString()
            val categoryName = newNote.categoryName
            val showDate = formatDate(newNote.createTime, newNote.modifyTime, isCreateTimeSort)

            // 优化：使用预构建的映射查找分类图标
            val categoryIcon = if (isAllAndUnCategorised(categoryIdStr)) {
                null
            } else {
                newNote.categoryColorIndex?.let { getCategoryIcon(it) }
            }

            // 新增：在ViewModel中处理显示信息
            val displayInfo = getNoteDisplayInfo(newNote, showDate,firstScreenThumbnailType)
            Logger.d("HomeNoteViewModel", "noteid: ${newNote.noteId} displayInfo: $displayInfo bitmapLastModifyMillis $bitmapLastModifyMillis")
            val content = newNote.content//原文本
            var summaryClear = cleanContentFontLineBreak(newNote.summary)
            val contentClear = content?.take(3000)//不展示全部文本 截取
            val contentBlocks = newNote.contents
            // 提取二期富文本结构
            val richV2 = contentBlocks?.filterIsInstance<EditorContent.RichTextV2>()?.firstOrNull()
            // 先判断是否有二期富文本 样式
            val adjustedRichTextStyleEntity =if (richV2!=null){
                offsetRichTextStyle(newNote,displayInfo, summaryClear)
            }else{
                // 将一期老数据（TextBlock/TodoBlock）转换成v2数据结构
                val textBlocks = newNote.contents?.filter {
                    it is EditorContent.TextBlock
                            || it is EditorContent.TodoBlock
                }
                val textBlocksToRichTextV2 =
                    RichTextBlockConverter.convertTextBlocksToRichTextV2(textBlocks)
                summaryClear=textBlocksToRichTextV2.plainText
                Logger.d("HomeNoteViewModel", "textBlocksToRichTextV2: $textBlocksToRichTextV2 summaryClear: $summaryClear")
                textBlocksToRichTextV2.richEntity
            }

            HomeNoteItemModel(
                noteId = newNote.noteId.toString(),
                noteTitle = displayInfo.primaryTitle.ifEmpty { newNote.title }, // 优先使用处理后的标题
                content = contentClear,
                summary = summaryClear,
                richTextStyleEntity = adjustedRichTextStyleEntity,
                titleResId = displayInfo.primaryTitleResId,
                categoryId = categoryIdStr, // 复用已转换的字符串
                categoryName = categoryName,
                date = showDate,
                createTime = newNote.createTime,
                modifyTime = newNote.modifyTime,
                thumbnailLastModifyMillis = bitmapLastModifyMillis,
                categoryIcon = categoryIcon,
                isChecked = false,
                // 缩略图相关信息（封装）
                thumbnailInfo = ThumbnailInfo(
                    type = displayInfo.thumbnailType,
                    showAudioIcon = displayInfo.showAudioIcon,
                    image = handwritingThumbnail ?: newNote.firstPicture,
                    handwritingThumbnailType = oldHandwritingThumbnail1,
                    handwritingThumbnail = handwritingThumbnail,
                    handwritingThumbnailDark= handwritingThumbnailDark,
                    firstPicture = newNote.firstPicture,
                    hasAudio = newNote.hasAudio == true
                )
            )
        }
    }
    Logger.d("HomeNoteViewModel", "mapToHomeNoteItems duration: ${timeMillis}ms, data transformation: ${notes.size} -> ${result.size}")
    return result
}

/**
 * 修复富文本样式偏移问题
 */
private fun offsetRichTextStyle(
    note: NoteListItem,
    displayInfo: NoteDisplayInfo,
    summaryClear: String
): RichTextStyleEntity? {
    val contentBlocks = note.contents
    // 提取二期富文本结构
    val richV2 = contentBlocks?.filterIsInstance<EditorContent.RichTextV2>()?.firstOrNull()
    val adjustedRichTextStyleEntity = richV2?.richTextStyleEntity?.let { styleEntity ->
        if (displayInfo.thumbnailType == ThumbnailType.PURE_TEXT) {
            val originalSummary = note.summary ?: ""
            val offsetAmount = originalSummary.length - summaryClear.length
            if (offsetAmount > 0) {
                // 如果移除了字符，需要向前偏移样式位置
                Logger.d(
                    "HomeNoteListUtils",
                    "Adjusting rich text style offset for note ${note.noteId}: original length=${originalSummary.length}, clear length=${summaryClear.length}, offset=-$offsetAmount"
                )
                styleEntity.offset(-offsetAmount)
            } else {
                styleEntity
            }
        } else {
            styleEntity
        }
    }
    return adjustedRichTextStyleEntity
}