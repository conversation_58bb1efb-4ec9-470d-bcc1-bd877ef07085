<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8" />
	<meta name="viewport"
		  content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
	<title>公式计算器</title>
	<script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>
<!--	<script type="text/javascript" src="js/tex-mml-chtml.js"></script>-->
	<script type="text/javascript" src="js/tex-svg.js" id="MathJax-script" async></script>
	<script>
        MathJax = {
            loader: {
                load: ['output/svg']
            },
            chtml: {
                displayAlign: 'left'
            },
            options: {
                enableMenu: false
            },
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                packages: {
                    '[+]': ['noerrors', 'noundefined', 'fix-unicode']
                },
                noundefined: {
                    color: '',
                    background: '',
                    size: ''
                },
                macros: {
                    oiint: "{\\bf ∯}",
                    oiiint: "{\\bf ∰}",
                    textperthousand: "{\\bf ‰}",
                    iddots: "{\\bf ⋰}",
                    ae: "{\\bf æ}",
                    copyright: "{\\bf ©}",
                    llbracket: "{\\bf ⟦}",
                    oe: "{\\bf œ}",
                    texteuro: "{\\bf €}",
                    fint: "{\\bf ⨏}",
                    rrbracket: "{\\bf ⟧}",
                    mapsfrom: "{\\bf ⟻}",
                    textwon: "{\\bf ₩}",
                    lightning: "{\\bf ↯}",
                    textregistered: "{\\bf ®}",
                    parr: "{\\bf ⅋}",
                    female: "{\\bf ♀}",
                    male: "{\\bf ♂}",
                    leftmoon: "{\\bf ☾}",
                    AE: "{\\bf Æ}",
                    mathsterling: "{\\bf £}",
                    sun: "{\\bf ☉}",
                    textcent: "{\\bf ₵}"
                }
            },
            //svg: {
            //    scale: 1,                      // global scaling factor for all expressions
            //    minScale: .5,                  // smallest scaling factor to use
            //    mtextInheritFont: false,       // true to make mtext elements use surrounding font
            //    merrorInheritFont: true,       // true to make merror text use surrounding font
            //    mathmlSpacing: false,          // true for MathML spacing rules, false for TeX rules
            //    skipAttributes: {},            // RFDa and other attributes NOT to copy to the output
            //    exFactor: .5,                  // default size of ex in em units
            //    displayAlign: 'center',        // default for indentalign when set to 'auto'
            //    displayIndent: '0',            // default for indentshift when set to 'auto'
            //    fontCache: 'local',            // or 'global' or 'none'
            //    localID: null,                 // ID to use for local font cache (for single equation processing)
            //    internalSpeechTitles: true,    // insert <title> tags with speech content
            //    titleID: 0                     // initial id number to use for aria-labeledby titles
            //},
            startup: {
                ready() {
                    const { Configuration } = MathJax._.input.tex.Configuration;
                    const { MapHandler } = MathJax._.input.tex.MapHandler;
                    const NodeUtil = MathJax._.input.tex.NodeUtil.default;
                    const { getRange } = MathJax._.core.MmlTree.OperatorDictionary;
                    function Other(parser, char) {
                        const font = parser.stack.env['font'];
                        let def = font ? { mathvariant: parser.stack.env['font'] } : {};
                        const remap = (MapHandler.getMap('remap')).lookup(char);
                        const range = getRange(char);
                        const type = range?.[3] || 'mo';
                        let mo = parser.create('token', type, def, (remap ? remap.char : char));
                        range?.[4] && mo.attributes.set('mathvariant', range[4]);
                        if (type === 'mo') {
                            NodeUtil.setProperty(mo, 'fixStretchy', true);
                            parser.configuration.addNode('fixStretchy', mo);
                        }
                        parser.Push(mo);
                    }
                    Configuration.create('fix-unicode', { fallback: { character: Other } });
                    MathJax.startup.defaultReady();
                }
            }
        };
	</script>
	<style>
		html,body {
			margin: 0;
			padding: 0;
		}

		#content {
			display: flex; width:100%; height:100%;overflow: auto;
			justify-content: safe center; /* 水平居中 */
            align-items: center;     /* 垂直居中 */
		}

	</style>
</head>
<body>
<div id="content" ondblclick="doubleClick()" white-space="normal" au></div>
<script type=" text/javascript" src="./js/web_api.js"></script>
<script>
		showLatex = function (latex,color,backgroundColor) {
			console.log(latex)
			const content=document.getElementById('content')
			content.setAttribute('style',`color:${color};background-color:${backgroundColor}`)
			content.innerHTML=`$$${latex}$$`
			MathJax.typeset([content]);
		}
		show = function (latex) {
			console.log(latex)
			const content=document.getElementById('content')
			content.innerHTML=`$$${latex}$$`
			MathJax.typeset([content]);
		}

		updateStyle = function (color,backgroundColor) {
			console.log("updateStyle")
			const content = document.getElementById('content')
			content.setAttribute('style',`color:${color};background-color:${backgroundColor}`)
		}

        function getRect() {
			let contentDiv = document.getElementById('content')
			Android.rectChangeClick(contentDiv.scrollWidth,contentDiv.scrollHeight)
        }

	</script>
<script>
        function exportSVG(formula) {
            try {
                let outputdiv = document.getElementById('output');  // 添加变量声明
                let wrapper = MathJax.tex2svg(`${formula}`, { em: 10, ex: 5, display: true });
                let svg = wrapper.getElementsByTagName("svg")[0];
                // 设置前景色
                const pathList = Array.from(svg.getElementsByTagName("path"));
                pathList.forEach(i => {
                    i.setAttribute("fill", "#0167fe");
                });
                const rectList = Array.from(svg.getElementsByTagName("rect"));
                rectList.forEach(i => {
                    i.setAttribute("fill", "#0167fe");
                });
                //console.log(svg.outerHTML);
                content.innerHTML = `$$${formula}$$`
                //window.chrome.webview.postMessage(svg.outerHTML);
                Android.svgCallback(svg.outerHTML);
                //return svg.outerHTML;  // 返回生成的 SVG
            } catch (error) {
                console.log(error);
                window.chrome.webview.postMessage(error);
            }
        }


        function exportImage(formula, color, scale, type) {
            try {
                let wrapper = MathJax.tex2svg(`\\large{${formula}}`, { em: 10, ex: 5, display: true })
                let output = { svg: "", img: "" }
                let mjOut = wrapper.getElementsByTagName("svg")[0]
                //set foreground
                const list = Array.from(mjOut.getElementsByTagName("g"));
                list.forEach(i => i.setAttribute("fill", `${color}`));

                output.svg = mjOut.outerHTML
                var image = new Image()
                image.src = 'data:image/svg+xml;base64,' + window.btoa(unescape(encodeURIComponent(output.svg)));
                image.onload = function () {
                    var canvas = document.createElement('canvas');
<!--                    let dpi = 2;-->
<!--                    if (window.devicePixelRatio > dpi) {-->
<!--                        dpi = window.devicePixelRatio;-->
<!--                    }-->
                    let dpi = scale;
                    let margin = 3;
                    let totalMargin = 6;
                    let w = Math.round(image.width * dpi);
                    let h = Math.round(image.height * dpi);
                    let m2 = Math.round(totalMargin * dpi);
                    canvas.width = w + m2;
                    canvas.height = h + m2;
                    var context = canvas.getContext('2d');
                    context.scale(dpi, dpi);
                    context.drawImage(image, margin, margin);

                    output.img = canvas.toDataURL('image/png');
                    console.log("type:"+type);
                    Android.imageCallback(output.img,type);
                    //return output.img;
                }
                image.onerror = function () {
                    window.chrome.webview.postMessage("");
                }
            } catch (error) {
            	alert(error)
                console.log(error);
                window.chrome.webview.postMessage(error);
            }
        }

</script>
<script type="text/javascript">
		function doubleClick() {
			Android.doubleClick()
		}
</script>


</body>

</html>
