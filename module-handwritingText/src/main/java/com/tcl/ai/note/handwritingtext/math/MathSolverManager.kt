package com.tcl.ai.note.handwritingtext.math

import android.content.Context
import android.graphics.RectF
import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import com.sunia.HTREngine.sdk.ERecognitionMode
import com.sunia.HTREngine.sdk.Engine
import com.sunia.HTREngine.sdk.Params
import com.sunia.HTREngine.sdk.RecognizeListener
import com.sunia.HTREngine.sdk.editor.Editor
import com.sunia.HTREngine.utils.LogUtil
import com.sunia.penengine.sdk.data.CurveDataArray
import com.sunia.penengine.sdk.data.RecoDatasInfoArray
import com.sunia.penengine.sdk.operate.edit.RecoDataType
import com.sunia.penengine.sdk.operate.edit.RecoGroupData
import com.sunia.penengine.sdk.operate.touch.PenType
import com.sunia.singlepage.sdk.InkFunc
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.ENABLE_DOT_CONVERT_MULTIPLICATION
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.ENABLE_MATH_COVERING
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.ENABLE_MIXTURE_BEATUTYCHARS
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.ENABLE_MIXTURE_CANDIATES_REPLACE
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.isCalculate
import com.tcl.ai.note.handwritingtext.utils.ViewLibUtils
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.ENABLE_MIXTURE_ERRORCORRECT
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.ENABLE_MIXTURE_NTI
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.ENABLE_MIXTURE_USE_EQUIDISTANT_NAI
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.ENABLE_MIXTURE_VERTICAL_RESULT_CALCULATE
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.ENABLE_REC_BALANCER_CHEM
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.ENABLE_RESULT_CORRECT_RATE
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.MERGE_PARAMS_STR
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.MIXTURE_BEATUTY_CORRENCTRATE
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.MIXTURE_MATH_CANDIATES_CORRENCT_RATE
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.MIXTURE_NBI_PARAMS
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.MIXTURE_REFINER_SPACING_CORRECT
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.NGRAM_PARAMS_STR
import com.tcl.ai.note.handwritingtext.utils.MathConstant.Companion.POST_PARAMS_STR
import com.tcl.ai.note.utils.Logger
import com.tcl.ff.component.utils.common.StringUtils
import java.lang.ref.WeakReference
import java.util.Locale

/**
 *  author : junze.liu
 *  date : 2025-08-15 19:15
 *  description : 数学公式计算管理器
 *  对接过程中遇到的有坑的地方：
 *  1、上报数据须和引擎回调线程保持一致，不然会导致上报的curveDataArray数据异常，导致闪退
 *  2、参数配置问题，params.setDataDir("mixture/conf/mixture_zh")，点击目录时一直跳转不过去，
 *    最后是在文件夹中删除assets下的全部文件，重新copy进来才解决的问题（导致的问题是，数据上报后，一直不回调onContentChanged方法）
 *  3、Demo中各种引擎回调耦合，跟sunia的同事确认，如果只是单页，不需要兼容多页的场景，单一混合引擎已适配产品需求
 *  （后续如果改为多页的话，这里可能需要做兼容）
 */
object MathSolverManager {

    private val TAG = "MathSolverManager"

    private val mixtureConfig: MixtureConfig = MixtureConfig()

    private var editor: Editor? = null

    private var inkFuncWeakRef: WeakReference<InkFunc>? = null

    private val groupDataMap: MutableList<MutableList<RecoGroupData>> = mutableListOf()

    private var chemGroupId = 0L

    private var recognizeHandler: Handler? = null

    private val DO_RECOGNIZE: Int = 1000

    fun init(engine: Engine?) {
        Logger.d(TAG, "-----init-----")
        initEditor(engine)
        initHandler()
    }


    private fun initEditor(engine: Engine?) {
        Logger.d(TAG, "initEngine, engine:$engine")
        if (engine == null || (!engine.isValidate)) {
            Logger.w(TAG, "initEditor, engine is null or isValidate, return")
            return
        }
        val start = System.currentTimeMillis()
        Logger.d(TAG, " initEditor start")

        if (editor == null) {
            val params = getParams()
            editor = engine.createEditor(params)
            Logger.d(TAG, "initEditor end, editor:$editor, use time:${System.currentTimeMillis() - start}, params:$params")
            editor?.setRecognizeEngineListener(object : RecognizeListener {
                override fun onLoaded(p0: Editor?) {
                    Logger.d(TAG, "editor onLoaded,  cost time:" + (System.currentTimeMillis() - start))
                }

                override fun onError(p0: Editor?, p1: Int, p2: Exception?) {
                    Logger.e(TAG, " editor onError, p1:$p1, p2:$p2")
                }

                override fun onContentChanged(p0: Editor, result: String?) {
                    Logger.d(TAG, "editor onContentChanged, result: $result")
                    if (!StringUtils.isEmpty(result)) {
                        parseRecognizeData(result.toString())
                    }
                }

                override fun onAssociationalChanged(p0: Editor?, p1: String?) {
                    Logger.d(TAG, "editor onAssociationalChanged, p1: $p1")
                }

                override fun onCandidateChanged(p0: Editor?, p1: String?) {
                    Logger.d(TAG, "editor onCandidateChanged, p1: $p1")
                }
            })

            editor?.open(params)
        }
        Logger.d(TAG, "initEditor, editor:$editor")
    }

    private fun initHandler() {
        Logger.d(TAG, "initHandler, recognizeHandler:$recognizeHandler")
        if (recognizeHandler == null){
            val handlerThread = HandlerThread("Rec_Th_" + hashCode())
            handlerThread.start()
            recognizeHandler = object : Handler(handlerThread.looper) {
                override fun handleMessage(msg: Message) {
                    super.handleMessage(msg)
                    Logger.d(TAG, "handleMessage, msg.what:${msg.what}")
                    when (msg.what) {
                        DO_RECOGNIZE -> {
                            doRecognize()
                        }
                    }
                }
            }
        }
    }


    /**
     * 当用户开始新笔迹时调用（按下事件）
     * @param startTime 新笔迹的开始时间
     */
    fun onActionDown(currentInkFunc: InkFunc?, isBrushMode: Boolean) {
        Logger.d(TAG, "onActionDown, ACTION_DOWN")
        if (isBrushMode && inkFuncWeakRef?.get() != currentInkFunc) {
            inkFuncWeakRef = WeakReference(currentInkFunc)
        }
        editor?.setWritingForMixture(true)
    }

    /**
     * 抬手时处理的业务逻辑
     */
    fun onActionUp() {
        Logger.d(TAG, "onActionUp, ACTION_UP")
        editor?.setWritingForMixture(false)
    }

    //和引擎线程必须是同一线程，不然curveDataArray会数据异常，导致闪退
    fun onDataUpdate(
        state: Int,
        curveDataArray: CurveDataArray?,
        tag: Any?,
        isFinish: Boolean
    ) {

        Logger.d(
            TAG,
            "addDataUpdate, state:$state, isFinish:$isFinish, curveDataArray:$curveDataArray"
        )

        if (!isCalculate) {
            Logger.d(TAG, "addDataUpdate, isCalculate is false , return")
            return
        }

        if (editor == null) {
            Logger.d(TAG, "addDataUpdate, editor is null")
            return
        }

        if (curveDataArray == null) {
            Logger.d(TAG, "addDataUpdate, curveDataArray is null")
            return
        }

        curveDataArray.createCurveData(getSupportCurveType())
        //只有是添加数据时，将数据传递给识别引擎
        if (state == 0) {
            Logger.d(TAG, "addDataUpdate, addPointsAndIDsForMixture")
            recognizeHandler?.removeMessages(DO_RECOGNIZE, tag)
            recognizeHandler?.post(Runnable {
                sendDataToSDK(curveDataArray)
            })
        }

        Logger.d(TAG, "addDataUpdate, isFinish:$isFinish")
        if (isFinish) {
            val msg = recognizeHandler?.obtainMessage()
            msg?.what = DO_RECOGNIZE
            msg?.obj = tag
            recognizeHandler?.sendMessageDelayed(msg!!, 10)
        }
    }

    /**
     * 兼容前进后退时能正常计算出识别结果
     */
    fun onRecoDataUpdate(recoDatasInfoArray: RecoDatasInfoArray) {
        if (editor == null) {
            LogUtil.e(TAG, "onRecoDataUpdate error by editor is null!")
            return
        }

        val recoDatasInfoArrayIndicator = recoDatasInfoArray.createRecoData(getSupportCurveType())

        if (recoDatasInfoArrayIndicator == 0L) {
            return
        }

        LogUtil.d(
            TAG,
            "onRecoDataUpdate start " + editor.hashCode() + " recoDatasInfoArrayIndicator " + recoDatasInfoArrayIndicator
        )

        recognizeHandler?.post(Runnable {
            val result = editor?.doInteractForMixture(recoDatasInfoArrayIndicator)
            LogUtil.d(TAG, "onRecoDataUpdate end, result: $result")
            recoDatasInfoArray.destroyRecoData(recoDatasInfoArrayIndicator)
        })
    }

    private fun sendDataToSDK(curveDataArray: CurveDataArray) {
        editor?.addPointsAndIDsForMixture(
            curveDataArray.xPtr,
            curveDataArray.yPtr,
            curveDataArray.tPtr,
            curveDataArray.actionPtr,
            curveDataArray.pointsLen,
            curveDataArray.strokePtr,
            curveDataArray.strokesLen
        )
        curveDataArray.destroyCurveData()
    }

    private fun doRecognize() {
        LogUtil.d(TAG, "doRecognize, -------trigger recognition-------")
        if (editor == null) {
            LogUtil.e(TAG, "doRecognize error by editor is null!")
            return
        }
        val time = System.currentTimeMillis()
        LogUtil.d(TAG, "doRecognize start " + editor.hashCode())
        editor?.setWritingForMixture(false)
        editor?.doPageRecognize(false)
        LogUtil.d(TAG, "doRecognize end time: " + (System.currentTimeMillis() - time) + "ms")
    }


    private fun getSupportCurveType(): IntArray? {
        return intArrayOf(
            PenType.PEN_INK.value, PenType.PEN_FOUNTAIN.value,
            PenType.PEN_PENCIL.value, PenType.PEN_BALLPOINT.value
        )
    }


    /**
     * 解析SDK返回的数据
     */
    fun parseRecognizeData(result: String) {
        val rectF = RectF()
        val list = ViewLibUtils.parseRecoBaseDatas(result, rectF)

        doRecoErrorCode(list)
        if (list == null || list.isEmpty()) {
            editor?.setWritingForMixture(true)
            return
        }
        checkResultOutside(list)
        inkFuncWeakRef?.get()?.iRecoOptFunc?.addRecognizeData(list)//设置回调结果 到手绘层
    }


    //处理脏数据
    private fun doRecoErrorCode(list: MutableList<RecoGroupData>?) {
        if (list.isNullOrEmpty()) {
            return
        }
        val errorList: MutableList<RecoGroupData> = mutableListOf()
        for (groupData in list) {
            if (groupData.mathErrorCode != 0) {
                errorList.add(groupData)
            }
        }

        if (errorList.isNotEmpty()) {
            list.removeAll(errorList)
            for (data in errorList) {
                if (data.mathErrorCode == MathErrorCode.eOverFlower.value || data.mathErrorCode == MathErrorCode.eInputNumberOverFlow.value) {
                    Logger.d(TAG, "doRecoErrorCode, Out of range")
                    break
                }
            }
        }
    }

    private fun checkResultOutside(vecRecoData: List<RecoGroupData?>) {
        Logger.d(TAG, "checkResultOutside vecRecoData:${vecRecoData.size}")
        if (vecRecoData.isEmpty()) {
            Logger.d(TAG, "checkResultOutside vecRecoData is empty")
            return
        }

        var tmpList: MutableList<RecoGroupData>? = null
        for (i in vecRecoData.indices) {
            vecRecoData[i]?.let { data ->
                refreshRecoGroupDataList(data)
                if (data.dataType == RecoDataType.Formula_Chem.value && data.recoCalcResultData != null) {
                    if (!tmpList.isNullOrEmpty()) {
                        Logger.i(TAG, "checkResultOutside, tmpList.size: ${tmpList.size} ")
                        groupDataMap.add(tmpList)
                    }
                    val chemList: MutableList<RecoGroupData> = ArrayList()
                    chemList.add(data)
                    groupDataMap.add(chemList)
                    tmpList = mutableListOf()
                } else {
                    if (tmpList == null) {
                        tmpList = mutableListOf()
                    }
                    tmpList.add(data)
                    if (i == vecRecoData.size - 1) {
                        Logger.d(TAG, "checkResultOutside: tmpList.size: ${tmpList.size}")
                        groupDataMap.add(tmpList)
                    }
                }
            }
        }

        Logger.d(TAG, "checkResultOutside, groupDataMap size:${groupDataMap.size} ")
        if (groupDataMap.isNotEmpty()) {
            val dataList: MutableList<RecoGroupData> = groupDataMap.removeAt(0)
            Logger.d(TAG, "checkResultOutside, dataList.size: ${dataList.size}")
            if (dataList.isNotEmpty()) {
                if (dataList[dataList.size - 1].groupId == chemGroupId) {
                    //把最后一条有计算结果的公示标记为true，SDK会自动判断是否超出屏幕,超出屏幕，自动选中
                    dataList[dataList.size - 1].bCheckResOutSrc = true
                }
            }
        }
    }


    private fun refreshRecoGroupDataList(groupData: RecoGroupData?) {
        groupData?.let { data ->
            // recoGroupDataList中块id相同未处理的数据，移除并添加新的数据
            removeGroupDataMap(data)
            if ((data.dataType == RecoDataType.Formula_Math.value || data.dataType == RecoDataType.Formula_Chem.value)
                && data.recoCalcResultData != null
            ) {
                chemGroupId = data.groupId
            }
        }
    }

    private fun removeGroupDataMap(data: RecoGroupData) {
        for (dataList in groupDataMap) {
            if (dataList.isEmpty()) {
                continue
            }
            var findIndex = -1
            for (i in dataList.indices) {
                if (dataList[i].groupId == data.groupId) {
                    findIndex = i
                }
            }
            if (findIndex != -1) {
                dataList.removeAt(findIndex)
            }
        }
    }

    /**
     * 组装参数
     */
    private fun getParams(): Params {
        Logger.d(TAG, "getParams start")
        val params: Params = Params.createInstance()
        params.mode = ERecognitionMode.MODE_ECR.value
        params.setDataDir("mixture/conf/mixture_zh")
        params.setConfigName("mixture-zh_CN.conf")
        params.setResultCoordinate(true)
        params.setWordSplitTimeLot(500)
        params.setResultAssociational(true)
        params.setResultCandidate(true)
        params.setResultPartitionCoordinate(true)
        params.setResultSpanProcessed(true)
        params.setResultCalculate(isCalculate)
        params.setKeyValuesConfig(buildParamsStr(GlobalContext.instance))
        Logger.d(TAG, "getParams end, params:$params")
        return params
    }


    private fun buildParamsStr(context: Context): String {
        ENABLE_MIXTURE_ERRORCORRECT = String.format(
            Locale.getDefault(),
            "enableMixtureErrorCorrect=%b;", mixtureConfig.errorCorrection
        )
        ENABLE_MIXTURE_CANDIATES_REPLACE = String.format(
            Locale.getDefault(),
            "enableMixtureCandiatesReplace=%b;", mixtureConfig.enableCandiates
        ) // 竖式开关
        ENABLE_MIXTURE_VERTICAL_RESULT_CALCULATE = String.format(
            Locale.getDefault(),
            "enableMixtureVerticalResultCalculate=%b;", mixtureConfig.enableVerticalResult
        ) // 竖式公式计算开关

        val ENABLE_MIXTURE_USE_BEATUTY_CORRECTRATE = null
        val MIXTURE_BEATUTY_LENGTH = null
        val MIX_NBI_FONTNAME = null
        val MIXTURE_REFINER = null
        val MIXTURE_REFINER_ENDING_CORRECT = null
        val builder = (NGRAM_PARAMS_STR +
                POST_PARAMS_STR +
                MERGE_PARAMS_STR +
                ENABLE_MATH_COVERING +
                ENABLE_DOT_CONVERT_MULTIPLICATION +
                ENABLE_RESULT_CORRECT_RATE +
                MIXTURE_MATH_CANDIATES_CORRENCT_RATE +
                ENABLE_MIXTURE_BEATUTYCHARS +
                ENABLE_MIXTURE_ERRORCORRECT +
                ENABLE_MIXTURE_CANDIATES_REPLACE +
                ENABLE_MIXTURE_VERTICAL_RESULT_CALCULATE +
                ENABLE_MIXTURE_NTI +
                ENABLE_MIXTURE_USE_EQUIDISTANT_NAI +
                ENABLE_MIXTURE_USE_BEATUTY_CORRECTRATE +
                MIXTURE_BEATUTY_CORRENCTRATE +
                MIXTURE_BEATUTY_LENGTH +
                MIXTURE_NBI_PARAMS +
                MIX_NBI_FONTNAME +
                ENABLE_REC_BALANCER_CHEM +
                MIXTURE_REFINER +
                MIXTURE_REFINER_SPACING_CORRECT +
                MIXTURE_REFINER_ENDING_CORRECT).toString() + String.format(
            Locale.getDefault(),
            "dpi=%f;",
            context.resources.displayMetrics.xdpi
        )
        return builder.trim { it <= ' ' }
    }

    class MixtureConfig {
        var beautify: Boolean = false
        var errorCorrection: Boolean = true
        var enableCandiates: Boolean = true
        var enableVerticalResult: Boolean = true

        override fun toString(): String {
            return "MixtureConfig{" +
                    ", beautify=" + beautify +
                    ", errorCorrection=" + errorCorrection +
                    ", enableCandiates=" + enableCandiates +
                    ", enableVerticalResult=" + enableVerticalResult +
                    '}'
        }
    }


}