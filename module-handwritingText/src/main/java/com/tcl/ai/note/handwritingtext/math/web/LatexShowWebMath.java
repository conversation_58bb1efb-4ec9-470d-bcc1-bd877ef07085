package com.tcl.ai.note.handwritingtext.math.web;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.AttributeSet;
import android.util.Base64;
import android.view.KeyEvent;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.annotation.NonNull;

import com.sunia.HTREngine.utils.LogUtil;

import java.util.ArrayList;
import java.util.List;

public class LatexShowWebMath extends WebView {
    private static final String TAG = "LatexShowWebMath";
    private volatile boolean isPageFinished = false;
    private volatile List<String> notExecuteUrlList = new ArrayList<>(1);
    private OnContentListener onContentListener;

    private String latex;
    private float webScale = 1f;

    public LatexShowWebMath(Context context) {
        super(context);
        initWebView();
    }

    public LatexShowWebMath(Context context, AttributeSet attrs) {
        super(context, attrs);
        initWebView();
    }

    @SuppressLint("JavascriptInterface")
    private void initWebView() {
        setVerticalScrollBarEnabled(false);
        setHorizontalScrollBarEnabled(false);
        WebSettings webSettings = getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setSupportMultipleWindows(true);
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.TEXT_AUTOSIZING);
        //这里要调用父类的，避免被我们过滤掉，导致没有执行
        super.loadUrl("file:///android_asset/webmath/index.html");
//        super.loadUrl("https://m.baidu.com/");
        setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideKeyEvent(WebView view, KeyEvent event) {
                return super.shouldOverrideKeyEvent(view, event);
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                LogUtil.d(TAG, "onPageFinished,url=" + url);
                //避免每次执行都重复执行
                if (isPageFinished) {
                    return;
                }
                isPageFinished = true;
                for (String notExecuteUrl : notExecuteUrlList) {
                    loadUrl(notExecuteUrl);
                }
                notExecuteUrlList.clear();
            }
        });
        //windows.Android.doubleClick()
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        setWebChromeClient(new WebChromeClient());
        addJavascriptInterface(new AndroidInterface(),"Android");
    }

    public void setWebScale(float scale) {
        this.webScale = scale;
    }

    public void showDefault(String text) {
        StringBuilder latexBuild = new StringBuilder();
        if (text.contains("\n")) {
            String latexMatrix = text.replaceAll("\n", "\\\\\\\\");
            latexBuild.append("\\begin{matrix}")
                    .append(latexMatrix)
                    .append("\\end{matrix}");
            text = latexBuild.toString();
        }
        text = text.replaceAll("\\\\", "$0$0");

        this.latex = text;
        String url = String.format("javascript:showLatex('%s','%s','%s')", text, "#151515", "#00000000");
        loadUrl(url);

        loadUrl("javascript:getRect()");
    }

    public void showSelected(String text) {
        text = text.replaceAll("\\\\", "$0$0");
        this.latex = text;
        String url = String.format("javascript:showLatex('%s','%s','%s')", text, "#0093E1", "#F5F5F5");
        loadUrl(url);

    }

    public void exportImage(){
        String url = String.format("javascript:exportImage(\"%s\",\"%s\",\"%f\",%d)", latex, "#151515",webScale * 3.0f,1);
        loadUrl(url);
    }

    public void exportImage(int type){
        LogUtil.d("FormulaRecognitionViewModel","exportImage, 导出图片");
        String url = String.format("javascript:exportImage(\"%s\",\"%s\",\"%f\",%d)", latex, "#151515",webScale * 3.0f, type);
        loadUrl(url);
    }

//    public void show(String text) {
//        text = text.replaceAll("\\\\", "$0$0");
//        String url = String.format("javascript:show('%s')", text);
//        loadUrl(url);
//    }
//
//    public void updateStyleDefault() {
//        String url = String.format("javascript:updateStyle('%s','%s')", "#151515", "#ffffff");
//        loadUrl(url);
//    }
//
//    public void updateStyleSelected() {
//        String url = String.format("javascript:updateStyle('%s','%s')", "#0093E1", "#F5F5F5");
//        loadUrl(url);
//    }

    @Override
    public void loadUrl(@NonNull String url) {
        if (isPageFinished) {
            super.loadUrl(url);
            return;
        }
        //缓存没有执行的指令
        notExecuteUrlList.add(url);
    }
    public void setOnContentListener(OnContentListener onContentListener){
        this.onContentListener = onContentListener;
    }

    public void clear() {
        showDefault("");
    }

    class AndroidInterface{

        @SuppressLint("JavascriptInterface")
        @JavascriptInterface
        public void imageCallback(String bmp,int type) {
            post(() -> {
                byte[] bitmapArray = Base64.decode(bmp.split(",")[1], Base64.DEFAULT);
                Bitmap bitmap = BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);
                if (onContentListener != null && bitmap != null){
                    onContentListener.onAddBitmap(bitmap,type);
                }
            });
        }
        @SuppressLint("JavascriptInterface")
        @JavascriptInterface
        public void svgCallback(String svgCode) {

        }
        @SuppressLint("JavascriptInterface")
        @JavascriptInterface
        public void doubleClick() {
            post(() -> {
                String url = String.format("javascript:exportImage(\"%s\",\"%s\",\"%f\",%d)", latex, "#151515",webScale * 3.0f,0);
                loadUrl(url);
            });
        }
        @SuppressLint("JavascriptInterface")
        @JavascriptInterface
        public void rectChangeClick(float w,float h) {
            post(() -> {
                if (onContentListener != null){
                    onContentListener.onSizeChange(w,h);
                }
            });
        }
    }

    public interface OnContentListener{
        void onAddBitmap(Bitmap bitmap,int type);

        void onSizeChange(float w,float h);
    }

}
