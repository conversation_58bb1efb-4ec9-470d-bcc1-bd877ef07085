package com.tcl.ai.note.handwritingtext.math

import android.graphics.Bitmap
import com.sunia.penengine.sdk.data.ICurve
import com.sunia.singlepage.sdk.InkFunc
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * 公式识别事件管理器
 * 用于传递公式识别事件
 */
object FormulaEventManager {

    private val _formulaRecognitionEvents = MutableSharedFlow<FormulaRecognitionEvent>()
    val formulaRecognitionEvents = _formulaRecognitionEvents.asSharedFlow()

    suspend fun sendFormulaRecognitionEvent(event: FormulaRecognitionEvent) {
        _formulaRecognitionEvents.emit(event)
    }


    suspend fun sendFormulaRecognitionEvent(bitmap: Bitmap?) {
        sendFormulaRecognitionEvent(
            FormulaRecognitionEvent(
                type = FormulaEventType.REPLACE,
                data = null,
                scale = 0f,
                offset = null,
                inkFunc = null,
                bitmap = bitmap,
            )
        )
    }
}

/**
 * FORMULA: 公式识别
 * REPLACE: 替换
 */
enum class FormulaEventType {
    FORMULA,REPLACE
}

data class FormulaRecognitionEvent(
    val type: FormulaEventType,
    val data: List<ICurve>?,
    val scale: Float,
    val offset: FloatArray?,
    val bitmap:Bitmap?,
    val inkFunc:InkFunc?,
)
