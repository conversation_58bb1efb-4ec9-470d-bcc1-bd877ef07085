package com.tcl.ai.note.handwritingtext.math;

/**
 * Created on 2025/1/7.
 * desc: Math Error code
 *
 * <AUTHOR> <PERSON>
 */
public enum MathErrorCode {
    eMathGood(0), //成功
    eSysErr(1), //系统错误
    eMathNotImp(2), //未实现
    eDevideByZero(21), //除数为0
    eValueIsNan(25), //不可以计算
    eExpressError(26), //表达式错误
    eInvalidArgument(27), //无效参数
    eOverFlower(28), //结果溢出
    eInvalidField(29), //无效的定义域
    eInputNumberOverFlow(30), // 输入的数值位数值超过15位
    eChemInvalidEquations(100), //无效化学方程式
    eChemUnableBlance(101), //无法配平化学方程式
    ;
    
    public int value = 0;

    MathErrorCode(int value) {
        this.value = value;
    }
}
