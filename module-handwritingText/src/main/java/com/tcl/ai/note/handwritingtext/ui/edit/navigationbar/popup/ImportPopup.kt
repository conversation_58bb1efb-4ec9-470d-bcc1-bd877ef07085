package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.popup

import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.unit.Density
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.utils.PopupOffsetUtils
import com.tcl.ai.note.handwritingtext.ui.popup.BounceScalePopup
import com.tcl.ai.note.handwritingtext.ui.popup.FocusedBounceScalePopup
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.ImportActionContent
import com.tcl.ai.note.handwritingtext.vm.TextAndDrawViewModel
import com.tcl.ai.note.theme.TclTheme

@Composable
fun ImportPopupComponent(
    isVisible: Boolean,
    density: Density,
    onDismiss: () -> Unit,
    onRecordAudio: () -> Unit,
    onChoosePhoto: () -> Unit,
    onTakePhoto: () -> Unit,
    isRecording: Boolean = false,
    textAndDrawViewModel: TextAndDrawViewModel = hiltViewModel()
) {
    if (!isVisible) return

    val importPopupOffset = PopupOffsetUtils.calculateImportPopupOffset(TclTheme.dimens, density)
    val isTextMode = textAndDrawViewModel.editMode == EditMode.TEXT

    val popupContent: @Composable (closePopup: () -> Unit) -> Unit = { closePopup ->
        ImportActionContent(
            onRecordAudio = {
                closePopup()
                onRecordAudio()
            },
            onChoosePhoto = {
                closePopup()
                onChoosePhoto()
            },
            onTakePhoto = {
                closePopup()
                onTakePhoto()
            },
            isRecording = isRecording
        )
    }

    if (isTextMode) {
        BounceScalePopup(
            onDismissRequest = onDismiss,
            offset = importPopupOffset,
            alignment = Alignment.TopEnd,
            enterTransformOrigin = TransformOrigin(0.65f, 0f),
            exitTransformOrigin = TransformOrigin(0.65f, 0f),
            content = popupContent
        )
    } else {
        FocusedBounceScalePopup(
            onDismissRequest = onDismiss,
            offset = importPopupOffset,
            alignment = Alignment.TopEnd,
            enterTransformOrigin = TransformOrigin(0.65f, 0f),
            exitTransformOrigin = TransformOrigin(0.65f, 0f),
            content = popupContent
        )
    }
}