package com.tcl.ai.note.handwritingtext.math.web;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.util.AttributeSet;
import android.util.Log;
import android.view.KeyEvent;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.annotation.NonNull;

import com.kspark.custom.utils.BitmapUtil;
import com.sunia.HTREngine.utils.LogUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 文本加载，获取加载后的图片
 */
public class TextShowWebMath extends WebView {
    private static final String TAG = "TextShowWebMath";
    private volatile boolean isPageFinished = false;
    private volatile List<String> notExecuteUrlList = new ArrayList<>(1);
    private OnContentListener onContentListener;

    private String text;

    public TextShowWebMath(Context context) {
        super(context);
        initWebView();
    }

    public TextShowWebMath(Context context, AttributeSet attrs) {
        super(context, attrs);
        initWebView();
    }

    @SuppressLint("JavascriptInterface")
    private void initWebView() {
        setVerticalScrollBarEnabled(false);
        setHorizontalScrollBarEnabled(false);
        WebSettings webSettings = getSettings();
        webSettings.setJavaScriptEnabled(false);
        webSettings.setSupportMultipleWindows(false);
        webSettings.setJavaScriptCanOpenWindowsAutomatically(false);
        webSettings.setUseWideViewPort(false);
        webSettings.setLoadWithOverviewMode(false);
//        webSettings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.TEXT_AUTOSIZING);
        //这里要调用父类的，避免被我们过滤掉，导致没有执行
//        super.loadUrl("file:///android_asset/webmathrecognize/recognize_math.html");
//        super.loadUrl("https://m.baidu.com/");
        setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideKeyEvent(WebView view, KeyEvent event) {
                return super.shouldOverrideKeyEvent(view, event);
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d(TAG, "onPageFinished,url=" + url);
                postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (view.getWidth() != 0 && view.getHeight() != 0 && onContentListener != null) {
                            //onContentListener.onAddBitmap(BitmapUtil.getBitmapFromView(TextShowWebMath.this));
                            onContentListener.onAddBitmap(BitmapUtil.screenshotWebView(TextShowWebMath.this));
                        }
                    }
                }, 2000);
                //避免每次执行都重复执行
                if (isPageFinished) {
                    return;
                }
                isPageFinished = true;
                for (String notExecuteUrl : notExecuteUrlList) {
                    loadUrl(notExecuteUrl);
                }
                notExecuteUrlList.clear();
            }
        });
        //windows.Android.doubleClick()
        setWebChromeClient(new WebChromeClient());
//        enableSlowWholeDocumentDraw();
        getSettings().setDefaultTextEncodingName("UTF -8");//设置默认为utf-8
        // 禁用缩放
        getSettings().setSupportZoom(false);

        // 设置WebView的布局尺寸与屏幕尺寸一致
//        setInitialScale(100);
    }

    public void showText(String text) {
        LogUtil.d(TAG, "showText,text=" + text);
        this.text = text;
        post(() -> loadDataWithBaseURL("", this.text, "text/html", "utf-8", ""));
    }

    @Override
    public void loadUrl(@NonNull String url) {
        if (isPageFinished) {
            super.loadUrl(url);
            return;
        }
        //缓存没有执行的指令
        notExecuteUrlList.add(url);
    }

    public void setOnContentListener(OnContentListener onContentListener) {
        this.onContentListener = onContentListener;
    }

    public void clear() {
        showText("");
    }

    @Override
    public void destroy() {
        Log.e(TAG, "onDestroy: ");
        this.onContentListener = null;
        stopLoading();
        setWebChromeClient(null);
        clearHistory();
        clearCache(true);
        clearFormData();
        super.destroy();
    }

    public interface OnContentListener {

        void onAddBitmap(Bitmap bitmap);
    }

}
