package com.tcl.ai.note.handwritingtext.math.web;

import android.text.TextUtils;
import android.webkit.JavascriptInterface;

public class JsImageObject {
    private JsImageCallback callback;

    public JsImageObject(JsImageCallback callback) {
        this.callback = callback;
    }

    @JavascriptInterface
    public void imageCallback(String base64Code) {
        if (callback != null && !TextUtils.isEmpty(base64Code)) {
            String base64Sub = base64Code.substring("data:image/png;base64,".length());
            callback.imageResult(base64Sub);
        }
    }

    @JavascriptInterface
    public void svgCallback(String svgCode) {
        if (callback != null) {
            callback.svgResult(svgCode);
        }
    }

    public interface JsImageCallback {
        void svgResult(String base64Code);

        void imageResult(String svgCode);
    }
}
