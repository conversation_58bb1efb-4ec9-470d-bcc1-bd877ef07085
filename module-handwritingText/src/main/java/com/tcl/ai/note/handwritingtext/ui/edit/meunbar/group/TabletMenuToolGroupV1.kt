package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.group

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.sunia.penengine.sdk.operate.touch.PenProp
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.bean.PenColor
import com.tcl.ai.note.handwritingtext.bean.menHullIcon
import com.tcl.ai.note.handwritingtext.bean.menNibIcon
import com.tcl.ai.note.handwritingtext.bean.toPenType
import com.tcl.ai.note.handwritingtext.ui.pen.PenTurnColorButton
import com.tcl.ai.note.handwritingtext.ui.popup.EraserToolPopup
import com.tcl.ai.note.handwritingtext.ui.popup.TabletPenColorPalettePopup
import com.tcl.ai.note.handwritingtext.ui.popup.TabletToolPopup
import com.tcl.ai.note.handwritingtext.vm.MenuBarUiState
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.menu.ColorGroupViewModel
import com.tcl.ai.note.handwritingtext.vm.menu.EraserViewModel
import com.tcl.ai.note.handwritingtext.vm.pen.PenToolbarViewModel
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.widget.DelayedBackgroundIconButton
import com.tcl.ai.note.widget.VerticalLine
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.isMarkerPen
import com.tcl.ai.note.utils.isFridaPhone


/**
 * 工具栏
 */
@Composable
fun TabletMenuToolGroupV1(
    penToolbarViewModel: PenToolbarViewModel = hiltViewModel(),
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel(),
    colorGroupViewModel: ColorGroupViewModel = hiltViewModel(),
    eraserViewModel: EraserViewModel = hiltViewModel(),
    menuBarState: MenuBarUiState,
    setPopupComposable: ((@Composable () -> Unit)?) -> Unit,
    onOpenEraser:() -> Unit,
    onOpenBrush:() -> Unit,
    onOpenKeyboard:() -> Unit,
    onOpenLasso:() -> Unit,
) {
    val dimens = getGlobalDimens()

    val isBrushActive = menuBarState.isBrushActive
    val isEraserActive = menuBarState.isEraserActive
    val isLassoActive = menuBarState.isLassoActive
    val isRulerActive = menuBarState.isRulerActive
    val isHandwritingToTextActive = menuBarState.isHandwritingToTextActive
    val isKeyboardActive = menuBarState.isKeyboardActive

    val selectedPen = penToolbarViewModel.selectedPen
    val initCount = penToolbarViewModel.initCount



    var lastTimeDismiss by remember { mutableLongStateOf(0L) }

    LaunchedEffect(initCount) {
        if(penToolbarViewModel.isInit){
            suniaDrawViewModel.switchBrush(PenProp().apply {
                penType = selectedPen.toPenType().value
                penColor = selectedPen.color.toArgb()
                penSize = selectedPen.width
                penAlpha = selectedPen.alpha
            })
        }
    }

    val closePopupComposable: () -> Unit = {

        setPopupComposable(null)
    }
    DisposableEffect(Unit) {
        onDispose {

            closePopupComposable()
        }
    }

    fun handleToolClick(
        offTimeThreshold: Long = 200L,
        popupSetter: @Composable () -> Unit
    ) {
        val offTime = System.currentTimeMillis() - lastTimeDismiss
        if (offTime > offTimeThreshold) {
            setPopupComposable {
                popupSetter() // 显示具体的工具弹窗
            }
        } else {
            closePopupComposable()
        }
    }

    val onEraserClick: (MenuBarItem) -> Unit = { menuBarItem ->
        onOpenEraser()
        if(menuBarItem.isChecked){
            handleToolClick(
                popupSetter = {
                    EraserToolPopup(
                        menuBarItem,
                        eraserViewModel = eraserViewModel,
                        suniaDrawViewModel = suniaDrawViewModel,
                        onDismissRequest = {
                            lastTimeDismiss = System.currentTimeMillis()
                            closePopupComposable()
                        },
                        onSwitchToBrush = {
                            onOpenBrush()
                        },
                    )
                }
            )
        }

    }

    val onBrushClick: (MenuBarItem) -> Unit = { menuBarItem ->
        onOpenBrush()
        if(menuBarItem.isChecked){
            handleToolClick(
                popupSetter = {
                    var isShowColorPicker  by remember { mutableStateOf(false) }
                    if(!isShowColorPicker){
                        TabletToolPopup(
                            menuBarItem = menuBarItem,
                            onOpenColorPicker = {
                                isShowColorPicker =true
                            },
                            onDismissRequest = {
                                if(!isShowColorPicker){
                                    lastTimeDismiss = System.currentTimeMillis()
                                    closePopupComposable()
                                }

                            },
                            onSwitchBrush = { penProp ->
                                suniaDrawViewModel.switchBrush(penProp)
                                colorGroupViewModel.cancelSelectColor()
                            },
                            onChangeBrushSize = { brushSize ->
                                suniaDrawViewModel.changePenSize(brushSize)
                            },
                            onChangePenColor = { penColor ->
                                suniaDrawViewModel.changePenColor(penColor.color.toArgb(),penColor.alpha)
                                colorGroupViewModel.cancelSelectColor()
                            }
                        )
                    }
                    if(isShowColorPicker){
                        TabletPenColorPalettePopup(
                            menuBarItem = menuBarItem,
                            curPenColor = PenColor(
                                color = penToolbarViewModel.selectedPen.color,
                                alpha = penToolbarViewModel.selectedPen.alpha
                            ),
                            onConfirm = { penColor ->
                                suniaDrawViewModel.changePenColor(penColor.color.toArgb(),penColor.alpha)
                                penToolbarViewModel.collectColor(penColor)
                                penToolbarViewModel.updateSelectedPen(color = penColor.color,alpha = penColor.alpha)
                                colorGroupViewModel.cancelSelectColor()
                            },
                            isSetAlpha = penToolbarViewModel.selectedPen.isMarkerPen() && !isFridaPhone,
                            onDismissRequest = {
                                isShowColorPicker =false
                                lastTimeDismiss = System.currentTimeMillis()
                                closePopupComposable()

                            }
                        )
                    }

                }
            )
        }

    }


    val onLassoClick: (MenuBarItem) -> Unit = { menuBarItem ->
        onOpenLasso()
    }


    val menuToolItems = remember(isBrushActive,isEraserActive,isLassoActive,isRulerActive,isHandwritingToTextActive,isKeyboardActive) {
        val menuBars = listOf(
            // MAIN 组 - 键盘按钮
            MenuBarItem.Keyboard.apply {
                isChecked = isKeyboardActive
                onClick = {
                    onOpenKeyboard()
                }
            },
            // EDIT 组 - 绘图工具
            MenuBarItem.Brush.apply {
                onClick = onBrushClick
                isChecked =
                    menuBarState.currentMenuType == MenuBar.BRUSH && isBrushActive
                iconRes =selectedPen.menHullIcon()
            },
            MenuBarItem.Eraser.apply {
                // 控制icon背景显示 - 添加弹窗状态判断
                isChecked =  isEraserActive
                onClick = onEraserClick
                iconRes =com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_eraser_nor
            },
            // TOOL 组 - 特殊绘图工具
             /*MenuBarItem.Lasso.apply {
                 isChecked = isLassoActive
                 onClick = onLassoClick
             },
            MenuBarItem.Ruler.apply {
                isChecked = isRulerActive
                onClick = {}
            },
            MenuBarItem.HandwritingToText.apply {
                isChecked = isHandwritingToTextActive
                onClick = {  }
            }*/
        )
        menuBars
    }

    val lastMenuType = menuToolItems.last().menuType

    Row(
        modifier = Modifier.height(dimens.menuBarHeight),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ){
        menuToolItems.forEachIndexed { index, item ->
            val modifierPos = Modifier.onGloballyPositioned { layoutCoordinates ->
                val position = layoutCoordinates.localToWindow(Offset.Zero)
                item.position = position

            }

            // 除了第一个按钮，其他按钮前面都加间距
            if (index > 1) {
                Spacer(Modifier.width(8.dp))
            }

            if(item.menuType == MenuBar.BRUSH){

                PenTurnColorButton(
                    modifier = modifierPos,
                    btnSize = item.btnSize,
                    iconSize = dimens.iconSize,
                    isChecked = item.isChecked,
                    onClick = { item.onClick.invoke(item) },
                    hullIconRes = selectedPen.menHullIcon(),
                    nibIconRes = selectedPen.menNibIcon(),
                    contentDescription = stringResource(item.descriptionRes),
                    penColor = selectedPen.color,
                )

            }else{
                DelayedBackgroundIconButton(
                    modifier = modifierPos,
                    btnSize = item.btnSize,
                    iconSize = dimens.iconSize,
                    painter = painterResource(item.iconRes!!),
                    isChecked = item.isChecked,
                    enabled = item.isEnabled,
                    contentDescription = stringResource(item.descriptionRes),
                    onClick = { item.onClick.invoke(item) }
                )
            }


            if (item.menuType == MenuBar.KEYBOARD) {
                Spacer(Modifier.width(12.dp))
                VerticalLine(
                    modifier = Modifier.height(20.dp),
                    color = colorResource(R.color.bg_outline_line_dark)
                )
                Spacer(Modifier.width(12.dp))
            } else if (item.menuType == lastMenuType) {
                Spacer(Modifier.width(12.dp))
            }
        }
    }
}