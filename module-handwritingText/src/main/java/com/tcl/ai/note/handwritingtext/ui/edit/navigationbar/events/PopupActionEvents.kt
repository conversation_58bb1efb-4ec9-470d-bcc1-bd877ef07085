package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.events

import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.handler.ImageHandlers
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.utils.AppActivityManager
import kotlinx.coroutines.flow.update


/**
 * 创建弹窗操作事件集合
 * 封装所有事件处理逻辑，使调用处更简洁
 */
internal fun createPopupActionEvents(
    imageHandlers: ImageHandlers,
    audioHandler: (() -> Unit) -> Unit,
    suniaDrawViewModel: SuniaDrawViewModel,
    enableFingerDrawing: Boolean,
    onDeleteNote: () -> Unit
) = PopupActionEvents(
    onRecordAudio = { audioHandler.invoke { } },
    onChoosePhoto = {
        suniaDrawViewModel.isInsertingImage = true
        AppActivityManager.isAddingImage.update { true }
        imageHandlers.pickImage.invoke { } },
    onTakePhoto = {
        suniaDrawViewModel.isInsertingImage = true
        AppActivityManager.isTakingPhoto.update { true }
        imageHandlers.takePhoto.invoke { } },
    onFingerDrawing = {
        suniaDrawViewModel.changeFingerDrawing(!enableFingerDrawing)
    },
    onDelete = onDeleteNote
)


/**
 * 点击事件写在这里
 * 弹框操作事件集合
 * 封装所有弹框相关的点击事件，避免参数过多
 */
data class PopupActionEvents(
    val onRecordAudio: () -> Unit = {},
    val onChoosePhoto: () -> Unit = {},
    val onTakePhoto: () -> Unit = {},
    val onFingerDrawing: (isOff: Boolean) -> Unit = {},
    val onDelete: () -> Unit = {}
)
