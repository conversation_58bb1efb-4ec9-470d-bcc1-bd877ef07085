package com.tcl.ai.note.handwritingtext.math.web;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.AttributeSet;
import android.util.Base64;
import android.util.Log;
import android.view.KeyEvent;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;

public class LatexImageWebMath extends WebView implements JsImageObject.JsImageCallback {
    private static final String TAG = "LatexSVGWebMath";

    public void setImageCallback(ImageCallback imageCallback) {
        this.imageCallback = imageCallback;
    }

    private ImageCallback imageCallback;

    public LatexImageWebMath(Context context) {
        super(context);
        initWebView();
    }

    public LatexImageWebMath(Context context, AttributeSet attrs) {
        super(context, attrs);
        initWebView();
    }

    @SuppressLint("JavascriptInterface")
    private void initWebView() {
        WebSettings webSettings = getSettings();
        webSettings.setJavaScriptEnabled(true);
        addJavascriptInterface(new JsImageObject(this), "javaObject");
        webSettings.setSupportMultipleWindows(true);
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        loadUrl("file:///android_asset/webmathrecognize/index_export.html");
        setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideKeyEvent(WebView view, KeyEvent event) {
                return super.shouldOverrideKeyEvent(view, event);
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
            }
        });
    }

    public void exportSVG(String latex) {
        latex = latex.replaceAll("\\\\", "$0$0");
        String url = String.format("javascript:exportSVG(\"%s\")", latex);
        loadUrl(url);
    }

    public void exportImage(String latex, float scale) {
        latex = latex.replaceAll("\\\\", "$0$0");
        String url = String.format("javascript:exportImage(\"%s\",\"%s\",\"%f\")", latex, "#000000",scale);
        loadUrl(url);
    }

    public void exportImage2(String latex,int width,int height) {
        latex = latex.replaceAll("\\\\", "$0$0");
        if (latex.startsWith(" ")) {
            Log.d(TAG,"exportImage2 ， latex:/"+latex+"/");
            latex = latex.substring(1);
            Log.d(TAG,"exportImage2 ， substring latex:/"+latex+"/");

        }
        String url = String.format("javascript:exportImage2(\"%s\",\"%s\",\"%d\",\"%d\")", latex, "#000000",width,height);
        Log.d(TAG,"exportImage2 ， url："+url);
        loadUrl(url);
    }

    @Override
    public void svgResult(String svgCode) {
        Log.d(TAG, "svgCode:" + svgCode);
        String outPath = new File(getContext().getExternalCacheDir(), "image.svg").getPath();
        writerStringToFile(outPath, svgCode);
    }

    @Override
    public void imageResult(String base64Code) {
        Log.d(TAG, "base64:" + base64Code);
        byte[] imageBytes = Base64.decode(base64Code, Base64.DEFAULT);
        Bitmap bitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.length);
        try {
            String outPath = new File(getContext().getExternalCacheDir(), "image.jpg").getPath();
            compressAndGenImage(bitmap, outPath);
            if (imageCallback != null) {
                imageCallback.onSuccess(bitmap);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void writerStringToFile(String filePath, String content) {
        FileWriter writer = null;
        try {
            writer = new FileWriter(filePath, true);
            writer.write(content);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (writer != null) {
                    writer.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void compressAndGenImage(Bitmap image, String outPath) throws IOException {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        // scale
        int options = 100;
        // Store the bitmap into output stream(no compress)
        image.compress(Bitmap.CompressFormat.JPEG, options, os);

        // Generate compressed image file
        FileOutputStream fos = new FileOutputStream(outPath);
        fos.write(os.toByteArray());
        Log.d(TAG, "compressAndGenImage--->file size：" + os.size() + "，compression ratio：" + options);
        fos.flush();
        fos.close();
    }

}
