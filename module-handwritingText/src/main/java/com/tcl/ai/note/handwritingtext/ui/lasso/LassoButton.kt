package com.tcl.ai.note.handwritingtext.ui.lasso

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.utils.HoverMutableInteractionSource
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.judge
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.handwritingtext.utils.ColorUtils.inverseColor
import com.tcl.ai.note.ripple.appRippleOrFallbackImplementation
import com.tcl.ai.note.utils.drawableRes
import kotlinx.coroutines.delay


@Composable
fun LassoButton(
    isDarkTheme:Boolean = isSystemInDarkTheme(),
    modifier: Modifier = Modifier,
    btnSize: Dp,
    iconSize:Dp,
    onClick: () -> Unit,
    isChecked: Boolean = false,
    @DrawableRes hullIconRes: Int,
    @DrawableRes lineIconRes: Int,
    contentDescription: String?
) {
    var showBackground by remember { mutableStateOf(false) }
    val hoverInteraction = remember {
        HoverMutableInteractionSource()
    }

    LaunchedEffect(isChecked) {
        if (isChecked) {
            delay(300)
            showBackground = true
        } else {
            showBackground = false
        }
    }

    Box(
        modifier = modifier
            .size(btnSize)
            .clickable (
                onClick = onClick,
                role = Role.Button,
                interactionSource = hoverInteraction,
                indication =
                appRippleOrFallbackImplementation(
                        bounded = false,
                        radius = btnSize / 2
                    )
            )
            .then(
                showBackground.judge(
                    Modifier.background(
                        color = R.color.tablet_btn_checked_bg.colorRes(),
                        shape = RoundedCornerShape(btnSize / 2),
                    ),
                    Modifier
                )
            ),
        contentAlignment = Alignment.Center
    ) {

        val tintColor=if(isDarkTheme) R.color.white else R.color.transparent_icon
        Image(
            modifier = Modifier.size(iconSize).padding(2.dp),
            painter = hullIconRes.drawableRes(),
            contentDescription = contentDescription,
            colorFilter = ColorFilter.tint(tintColor.colorRes()),
        )
        Image(
            modifier = Modifier.size(iconSize).padding(4.dp),
            painter = lineIconRes.drawableRes(),
            contentDescription = contentDescription,
            colorFilter = ColorFilter.tint(tintColor.colorRes()),
        )
    }
}
