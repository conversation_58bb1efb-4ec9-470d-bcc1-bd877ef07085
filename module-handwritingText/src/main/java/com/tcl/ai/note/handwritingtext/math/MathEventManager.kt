package com.tcl.ai.note.handwritingtext.math

import com.sunia.penengine.sdk.data.CurveDataArray
import com.sunia.penengine.sdk.data.ListCurve
import com.sunia.penengine.sdk.data.RecoDatasInfoArray
import com.sunia.singlepage.sdk.InkFunc
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * 数学公式计算事件管理器
 * 用于传递数学公式计算的事件
 */
object MathEventManager {

    private val _mathRecognitionEvents = MutableSharedFlow<MathRecognitionEvent>()
    val mathRecognitionEvents = _mathRecognitionEvents.asSharedFlow()

    suspend fun sendMathRecognitionEvent(event: MathRecognitionEvent) {
        _mathRecognitionEvents.emit(event)
    }


    suspend fun sendMathRecognitionEvent(curveList: ListCurve?, inkFunc: InkFunc) {
        sendMathRecognitionEvent(
            MathRecognitionEvent(
                type = MathEventType.MATH,
                state = 1,
                curveDataArray = null,
                recoDatasInfoArray = null,
                recoDatasInfoArrayIndicator = 0,
                tag = null,
                inkFunc = null,
                isFinish = false
            )
        )
    }
}

enum class MathEventType {
    ACTION_DOWN, ACTION_UP, MATH,REDO
}

data class MathRecognitionEvent(
    val inkFunc: InkFunc?,
    val type: MathEventType,
    val state: Int,
    val curveDataArray: CurveDataArray?,
    val recoDatasInfoArrayIndicator: Long,
    val recoDatasInfoArray: RecoDatasInfoArray?,
    val tag: Any?,
    val isFinish: Boolean,
)
