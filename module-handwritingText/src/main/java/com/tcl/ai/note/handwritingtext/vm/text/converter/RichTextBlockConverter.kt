package com.tcl.ai.note.handwritingtext.vm.text.converter

import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle
import com.tcl.ai.note.handwritingtext.repo.EntHelper
import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.handwritingtext.richtext.data.StyleRange
import com.tcl.ai.note.handwritingtext.utils.RichTextLayoutUtils
import com.tcl.ai.note.handwritingtext.utils.RichTextMetricPx
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.ceil

/**
 * 一期块列表转二期富文本数据辅助类
 */
object RichTextBlockConverter {
    /**
     * 一期块转富文本结构结果
     */
    data class RichTextConvertResult(
        val plainText: String,
        val richEntity: RichTextStyleEntity,
        // 还原图片用
        val entHelpers: List<EntHelper>,
    )

    /**
     * 一期所有内容块（TextBlock、TodoBlock）转换为二期RichTextV2及plainText字符串
     * 规则：
     * - TextBlock 转普通段落，保留原有内容，分段样式同步到list字段
     * - TodoBlock 不加任何符号前缀，样式value=todo, checked=block.isDone
     * - span样式全部平移，区间相对全局文本偏移
     */
    suspend fun convertBlocksToRichTextV2(
        blocks: List<EditorContent>
    ) = withContext(Dispatchers.Default) {
        var curOffset = 0
        val plainBuilder = StringBuilder()
        val bold = mutableListOf<StyleRange>()
        val italic = mutableListOf<StyleRange>()
        val underline = mutableListOf<StyleRange>()
        val strikethrough = mutableListOf<StyleRange>()
        val list = mutableListOf<StyleRange>()

        var currentNumberedIndex = 1
        var lastListType: ParagraphStyle? = null

        val entHelpers = mutableListOf<EntHelper>()

        blocks.forEach { block ->
            // 只转换文本块TextBlock和待办事项TodoBlock内容
            when (block) {
                is EditorContent.TextBlock -> {
                    val text = block.text.text
                    val paraType = when (block.paragraphStyle) {
                        ParagraphStyle.NUMBERED -> "number"
                        ParagraphStyle.BULLETED -> "bullet"
                        else -> "text"
                    }

                    val blockStartInGlobal = curOffset
                    plainBuilder.append(text)

                    // 处理段落/列表样式
                    var lineStartInBlock = 0
                    val lines = text.split('\n')
                    lines.forEachIndexed { lineIndex, line ->
                        val lineLen = line.length
                        val lineStartInGlobal = blockStartInGlobal + lineStartInBlock
                        val lineEndInGlobal = lineStartInGlobal + lineLen

                        // 添加列表样式
                        if (paraType == "number") {
                            if (lastListType != ParagraphStyle.NUMBERED)
                                currentNumberedIndex = 1
                            list.add(
                                StyleRange(
                                start = lineStartInGlobal,
                                end = lineEndInGlobal,
                                value = paraType,
                                number = currentNumberedIndex
                            )
                            )
                            currentNumberedIndex++
                        } else if (paraType == "bullet") {
                            list.add(
                                StyleRange(
                                start = lineStartInGlobal,
                                end = lineEndInGlobal,
                                value = paraType,
                                number = 0
                            )
                            )
                            currentNumberedIndex = 1
                        } else {
                            currentNumberedIndex = 1
                        }
                        lastListType = block.paragraphStyle

                        lineStartInBlock += lineLen
                        if (lineIndex < lines.size - 1) {
                            lineStartInBlock += 1  // 换行符
                        }
                    }

                    // 修正spans的end值
                    val rawSpans = block.text.annotatedString.spanStyles
                    val correctedSpans = if (rawSpans.size > 1) {
                        // 按start位置排序
                        val sortedSpans = rawSpans.sortedBy { it.start }

                        sortedSpans.mapIndexed { index, span ->
                            val correctedEnd = if (index < sortedSpans.size - 1) {
                                // 不是最后一个span，end设为下一个span的start
                                val nextSpanStart = sortedSpans[index + 1].start
                                minOf(nextSpanStart, text.length)
                            } else {
                                // 最后一个span，使用原始end值，但限制在文本长度内
                                minOf(span.end, text.length)
                            }

                            CorrectedSpan(
                                start = span.start,
                                end = correctedEnd,
                                style = span.item
                            )
                        }
                    } else if (rawSpans.size == 1) {
                        // 只有一个span，直接使用，但限制end值
                        listOf(CorrectedSpan(
                            start = rawSpans[0].start,
                            end = minOf(rawSpans[0].end, text.length),
                            style = rawSpans[0].item
                        ))
                    } else {
                        emptyList()
                    }

                    // 应用修正后的spans
                    correctedSpans.forEach { span ->
                        if (span.start >= 0 && span.end <= text.length && span.start < span.end) {
                            val globalStart = blockStartInGlobal + span.start
                            val globalEnd = blockStartInGlobal + span.end

                            if (span.style.fontWeight == FontWeight.Bold) {
                                bold.add(StyleRange(globalStart, globalEnd, ""))
                            }
                            if (span.style.fontStyle == FontStyle.Italic) {
                                italic.add(StyleRange(globalStart, globalEnd, ""))
                            }
                            if (span.style.textDecoration?.contains(TextDecoration.Underline) == true) {
                                underline.add(StyleRange(globalStart, globalEnd, ""))
                            }
                            // 处理原有的删除线样式（由于一期只有已完成的待办事项才有删除线功能，所以无需处理删除线）
//                            if (span.style.textDecoration?.contains(TextDecoration.LineThrough) == true) {
//                                strikethrough.add(StyleRange(globalStart, globalEnd, ""))
//                            }
                        }
                    }

                    curOffset += text.length
                    plainBuilder.append('\n')
                    curOffset += 1

                    // 添加该文本框高度，用于图片还原
                    entHelpers.add(
                        EntHelper.Text(
                            height = RichTextLayoutUtils.getTextHeightPx(
                                block.text.text,
                                block.paragraphStyle != ParagraphStyle.NONE
                            )
                        )
                    )
                }

                is EditorContent.TodoBlock -> {
                    val text = block.text.text
                    val blockStartInGlobal = curOffset

                    plainBuilder.append(text)

                    // 添加todo样式
                    list.add(
                        StyleRange(
                        start = blockStartInGlobal,
                        end = blockStartInGlobal + text.length,
                        value = "todo",
                        checked = block.isDone
                    )
                    )

                    // 为已完成的待办事项添加删除线
                    if (block.isDone && text.isNotEmpty()) {
                        strikethrough.add(
                            StyleRange(
                            start = blockStartInGlobal,
                            end = blockStartInGlobal + text.length,
                            value = ""
                        )
                        )
                    }

                    // 处理TodoBlock的spans
                    val rawSpans = block.text.annotatedString.spanStyles
                    val correctedSpans = if (rawSpans.size > 1) {
                        val sortedSpans = rawSpans.sortedBy { it.start }
                        sortedSpans.mapIndexed { index, span ->
                            val correctedEnd = if (index < sortedSpans.size - 1) {
                                val nextSpanStart = sortedSpans[index + 1].start
                                minOf(nextSpanStart, text.length)
                            } else {
                                minOf(span.end, text.length)
                            }
                            CorrectedSpan(span.start, correctedEnd, span.item)
                        }
                    } else if (rawSpans.size == 1) {
                        listOf(CorrectedSpan(
                            start = rawSpans[0].start,
                            end = minOf(rawSpans[0].end, text.length),
                            style = rawSpans[0].item
                        ))
                    } else {
                        emptyList()
                    }

                    correctedSpans.forEach { span ->
                        if (span.start >= 0 && span.end <= text.length && span.start < span.end) {
                            val globalStart = blockStartInGlobal + span.start
                            val globalEnd = blockStartInGlobal + span.end

                            if (span.style.fontWeight == FontWeight.Bold) {
                                bold.add(StyleRange(globalStart, globalEnd, ""))
                            }
                            if (span.style.fontStyle == FontStyle.Italic) {
                                italic.add(StyleRange(globalStart, globalEnd, ""))
                            }
                            if (span.style.textDecoration?.contains(TextDecoration.Underline) == true) {
                                underline.add(StyleRange(globalStart, globalEnd, ""))
                            }

                            // 处理TodoBlock中原有的删除线样式（由于一期只有已完成的待办事项才有删除线功能，所以无需处理非完成状态删除线）
                            // 注意：这里不处理完成状态的删除线，因为已经在上面统一处理了
//                            if (!block.isDone && span.style.textDecoration?.contains(TextDecoration.LineThrough) == true) {
//                                strikethrough.add(StyleRange(globalStart, globalEnd, ""))
//                            }
                        }
                    }

                    curOffset += text.length
                    plainBuilder.append('\n')
                    curOffset += 1

                    currentNumberedIndex = 1
                    lastListType = null

                    entHelpers.add(
                        EntHelper.Text(
                            height = RichTextLayoutUtils.getTextHeightPx(
                                block.text.text,
                                true,
                            )
                        )
                    )
                }

                is EditorContent.ImageBlock -> {
                    // 涉及到text行高，需要在主线程触发初始化
                    withContext(Dispatchers.Main) {
                        val size = RichTextLayoutUtils.getImageSizePx(block)
                        val emptyLine =
                            ceil(size.height.toFloat() / RichTextMetricPx.measuredLineHeight).toInt()
                        // 插入空行，保留还原图片位置
                        repeat(emptyLine) { plainBuilder.append('\n') }
                        curOffset += emptyLine
                        // 状态还原
                        currentNumberedIndex = 1
                        lastListType = null
                        val height = emptyLine * RichTextMetricPx.measuredLineHeight
                        val path = block.uri.path
                        entHelpers.add(
                            EntHelper.Image(
                                size = size,
                                path = path
                                    ?: "",
                                height = height,
                            )
                        )
                    }
                }

                else -> {
                    currentNumberedIndex = 1
                    lastListType = null
                }
            }
        }

        RichTextConvertResult(
            plainText = plainBuilder.toString(),
            richEntity = RichTextStyleEntity(
                bold = bold,
                italic = italic,
                underline = underline,
                strikethrough = strikethrough,
                list = list
            ),
            entHelpers = entHelpers,
        )
    }

    /**
     * 简化版本：适用于首页缩略图 显示一期文本
     */
    fun convertTextBlocksToRichTextV2(
        blocks: List<EditorContent>?
    ): RichTextConvertResult {
        var curOffset = 0
        val plainBuilder = StringBuilder()
        val bold = mutableListOf<StyleRange>()
        val italic = mutableListOf<StyleRange>()
        val underline = mutableListOf<StyleRange>()
        val strikethrough = mutableListOf<StyleRange>()
        val list = mutableListOf<StyleRange>()

        var currentNumberedIndex = 1
        var lastListType: ParagraphStyle? = null


        blocks?.forEach { block ->
            when (block) {
                is EditorContent.TextBlock -> {
                    val text = block.text.text
                    val paraType = when (block.paragraphStyle) {
                        ParagraphStyle.NUMBERED -> "number"
                        ParagraphStyle.BULLETED -> "bullet"
                        else -> "text"
                    }

                    val blockStartInGlobal = curOffset
                    plainBuilder.append(text)

                    // 处理段落/列表样式
                    var lineStartInBlock = 0
                    val lines = text.split('\n')
                    lines.forEachIndexed { lineIndex, line ->
                        val lineLen = line.length
                        val lineStartInGlobal = blockStartInGlobal + lineStartInBlock
                        val lineEndInGlobal = lineStartInGlobal + lineLen

                        // 添加列表样式
                        if (paraType == "number") {
                            if (lastListType != ParagraphStyle.NUMBERED)
                                currentNumberedIndex = 1
                            list.add(
                                StyleRange(
                                    start = lineStartInGlobal,
                                    end = lineEndInGlobal,
                                    value = paraType,
                                    number = currentNumberedIndex
                                )
                            )
                            currentNumberedIndex++
                        } else if (paraType == "bullet") {
                            list.add(
                                StyleRange(
                                    start = lineStartInGlobal,
                                    end = lineEndInGlobal,
                                    value = paraType,
                                    number = 0
                                )
                            )
                            currentNumberedIndex = 1
                        } else {
                            currentNumberedIndex = 1
                        }
                        lastListType = block.paragraphStyle

                        lineStartInBlock += lineLen
                        if (lineIndex < lines.size - 1) {
                            lineStartInBlock += 1  // 换行符
                        }
                    }

                    // 处理spans样式
                    processSpansForBlock(
                        block.text.annotatedString.spanStyles,
                        text,
                        blockStartInGlobal,
                        bold,
                        italic,
                        underline,
                        strikethrough
                    )

                    curOffset += text.length
                    plainBuilder.append('\n')
                    curOffset += 1

                }

                is EditorContent.TodoBlock -> {
                    val text = block.text.text
                    val blockStartInGlobal = curOffset

                    plainBuilder.append(text)

                    // 添加todo样式
                    list.add(
                        StyleRange(
                            start = blockStartInGlobal,
                            end = blockStartInGlobal + text.length,
                            value = "todo",
                            checked = block.isDone
                        )
                    )

                    // 为已完成的待办事项添加删除线
                    if (block.isDone && text.isNotEmpty()) {
                        strikethrough.add(
                            StyleRange(
                                start = blockStartInGlobal,
                                end = blockStartInGlobal + text.length,
                                value = ""
                            )
                        )
                    }

                    // 处理spans样式
                    processSpansForBlock(
                        block.text.annotatedString.spanStyles,
                        text,
                        blockStartInGlobal,
                        bold,
                        italic,
                        underline,
                        strikethrough
                    )

                    curOffset += text.length
                    plainBuilder.append('\n')
                    curOffset += 1

                    currentNumberedIndex = 1
                    lastListType = null
                }
                is EditorContent.ImageBlock -> {
                    // 涉及到text行高，需要在主线程触发初始化
//                        val size = RichTextLayoutUtils.getImageSizePx(block)
//                        val emptyLine =
//                            ceil(size.height.toFloat() / RichTextMetricPx.measuredLineHeight).toInt()
//                        // 插入空行，保留还原图片位置
//                        repeat(emptyLine) { plainBuilder.append('\n') }
//                        curOffset += emptyLine
//                        // 状态还原
//                        currentNumberedIndex = 1
//                        lastListType = null
//                        Logger.d("ImageBlock size: $size emptyLine $emptyLine curOffset $curOffset")
                    }
                else -> {
                    // 忽略其他类型的block（如ImageBlock）
                    currentNumberedIndex = 1
                    lastListType = null
                }
            }
        }

        return RichTextConvertResult(
            plainText = plainBuilder.toString(),
            richEntity = RichTextStyleEntity(
                bold = bold,
                italic = italic,
                underline = underline,
                strikethrough = strikethrough,
                list = list
            ),
            entHelpers =emptyList() ,
        )
    }

    /**
     * 处理spans样式的通用方法
     */
    private fun processSpansForBlock(
        rawSpans: List<AnnotatedString.Range<SpanStyle>>,
        text: String,
        blockStartInGlobal: Int,
        bold: MutableList<StyleRange>,
        italic: MutableList<StyleRange>,
        underline: MutableList<StyleRange>,
        strikethrough: MutableList<StyleRange>
    ) {
        val correctedSpans = if (rawSpans.size > 1) {
            // 按start位置排序
            val sortedSpans = rawSpans.sortedBy { it.start }
            sortedSpans.mapIndexed { index, span ->
                val correctedEnd = if (index < sortedSpans.size - 1) {
                    // 不是最后一个span，end设为下一个span的start
                    val nextSpanStart = sortedSpans[index + 1].start
                    minOf(nextSpanStart, text.length)
                } else {
                    // 最后一个span，使用原始end值，但限制在文本长度内
                    minOf(span.end, text.length)
                }
                CorrectedSpan(
                    start = span.start,
                    end = correctedEnd,
                    style = span.item
                )
            }
        } else if (rawSpans.size == 1) {
            // 只有一个span，直接使用，但限制end值
            listOf(
                CorrectedSpan(
                    start = rawSpans[0].start,
                    end = minOf(rawSpans[0].end, text.length),
                    style = rawSpans[0].item
                )
            )
        } else {
            emptyList()
        }

        // 应用修正后的spans
        correctedSpans.forEach { span ->
            if (span.start >= 0 && span.end <= text.length && span.start < span.end) {
                val globalStart = blockStartInGlobal + span.start
                val globalEnd = blockStartInGlobal + span.end

                if (span.style.fontWeight == FontWeight.Bold) {
                    bold.add(StyleRange(globalStart, globalEnd, ""))
                }
                if (span.style.fontStyle == FontStyle.Italic) {
                    italic.add(StyleRange(globalStart, globalEnd, ""))
                }
                if (span.style.textDecoration?.contains(TextDecoration.Underline) == true) {
                    underline.add(StyleRange(globalStart, globalEnd, ""))
                }
            }
        }
    }
    /**
     * 修正后的Span数据类
     */
    private data class CorrectedSpan(
        val start: Int,
        val end: Int,
        val style: SpanStyle
    )
}