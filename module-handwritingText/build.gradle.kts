plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.jetbrains.kotlin.android)
    alias(libs.plugins.google.dagger.hilt)
    alias(libs.plugins.google.devtools.ksp)
    alias(libs.plugins.compose.compiler)
    id("org.jetbrains.kotlin.plugin.serialization")
}
val standaloneHandwritingText = project.findProperty("standaloneHandwritingText")?.toString()?.toBoolean() ?: false
android {
    namespace = "com.tcl.ai.note.handwritingtext"
    compileSdk = libs.versions.compileSdk.get().toIntOrNull() ?: 35

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toIntOrNull() ?: 34

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    flavorDimensions.add("device")
    productFlavors {
        create("phone") {
            dimension = "device"
            buildConfigField("boolean", "IS_PHONE", "true")
        }
        create("tablet") {
            dimension = "device"
            buildConfigField("boolean", "IS_PHONE", "false")
        }
    }

    sourceSets {
        getByName("phone") {
            java.srcDirs("src/phone/java")
            res.srcDirs("src/phone/res")
            manifest.srcFile("src/phone/AndroidManifest.xml")
        }
        getByName("tablet") {
            java.srcDirs("src/tablet/java")
            res.srcDirs("src/tablet/res")
            manifest.srcFile("src/tablet/AndroidManifest.xml")
        }
    }
    /*sourceSets {
        getByName("main") {
            if (standaloneHandwritingText) {
                manifest.srcFile("src/main/moduleManifest/AndroidManifest.xml")
            } else {
                manifest.srcFile("src/main/AndroidManifest.xml")
            }
        }
    }*/
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }
    buildFeatures {
        buildConfig = true
        viewBinding = true
        compose = true
    }

}
ksp {
    arg("room.schemaLocation", "$projectDir/schemas")
}
dependencies {
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    implementation(libs.androidx.adaptive)
    implementation(libs.androidx.lifecycle.service)
    implementation(libs.tcl.componentfrm)
    implementation(libs.androidx.datastore.preferences)
    implementation(libs.androidx.fragment)
    implementation(libs.androidx.appcompat)
    implementation(libs.dagger.hilt)
    implementation(libs.androidx.constraintlayout.compose.android)
    implementation(libs.androidx.constraintlayout)
    implementation(project(":module-voiceToText"))
    implementation(project(":module-journal:template"))
    ksp(libs.dagger.hilt.compiler)
    implementation(libs.androidx.lifecycle.viewmodel.compose)
    implementation(libs.androidx.navigation.compose)
    implementation(libs.androidx.hilt.navigation.compose)
    implementation(libs.okhttp3.okhttp)
    implementation(libs.retrofit)
    implementation(libs.airbnb.lottie.compose)
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx)
    ksp(libs.androidx.room.compiler)
    implementation(libs.coil.network.okhttp)
    implementation(libs.compose.ui)
    implementation(libs.compose.material)
    implementation(libs.compose.ui.tooling)
    implementation(libs.compose.foundation)
    implementation(libs.kotlinx.serialization.core)
    implementation(libs.kotlinx.serialization.protobuf)
    implementation(libs.tclui.compose)
    implementation(libs.tclui.compose.icons)
    lintChecks(libs.tclui.compose.lint)
    implementation("io.coil-kt:coil:2.6.0")
    implementation("io.coil-kt:coil-gif:2.6.0")
    implementation("io.coil-kt:coil-compose:2.6.0")


    // Low latency graphics
    implementation (libs.androidx.graphics.core)
    // tct Bi
    implementation(libs.bundles.tctBI)
    implementation("com.tcl.ff.component:http:1.2.3") {
        exclude(group = "xmlpull", module = "xmlpull")
    }
    implementation(project(":module-base"))
    implementation(project(":sdk:lib-beautify"))
    implementation(project(":module-sunia"))
    implementation(project(":sdk:lib-sunia"))
    implementation(project(":sdk:lib-sunia-auth"))
    implementation(project(":sdk:lib-sunia-estimate"))
    implementation(project(":sdk:lib-sunia-single"))
    implementation(project(":sdk:lib-sunia-recognize"))
    implementation(libs.kotlinx.serialization.json)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}