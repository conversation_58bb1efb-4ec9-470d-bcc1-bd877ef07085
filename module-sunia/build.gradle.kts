plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.jetbrains.kotlin.android)
}

android {
    namespace = "com.tcl.ai.note.sunia"
    compileSdk = libs.versions.compileSdk.get().toIntOrNull() ?: 35

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toIntOrNull() ?: 34

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }

    buildFeatures {
        buildConfig = true
    }

    flavorDimensions.add("device")
    productFlavors {
        create("phone") {
            dimension = "device"
            buildConfigField("boolean", "IS_PHONE", "true")
        }
        create("tablet") {
            dimension = "device"
            buildConfigField("boolean", "IS_PHONE", "false")
        }
    }

    sourceSets {
        getByName("phone") {
            java.srcDirs("src/phone/java")
            res.srcDirs("src/phone/res")
            manifest.srcFile("src/phone/AndroidManifest.xml")
        }
        getByName("tablet") {
            java.srcDirs("src/tablet/java")
            res.srcDirs("src/tablet/res")
            manifest.srcFile("src/tablet/AndroidManifest.xml")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }
}

dependencies {
    implementation(project(":module-base"))
    implementation(project(":sdk:lib-sunia"))
    implementation(project(":sdk:lib-sunia-auth"))
    implementation(project(":sdk:lib-sunia-estimate"))
    implementation(project(":sdk:lib-sunia-single"))
    implementation(project(":sdk:lib-sunia-recognize"))

    implementation(libs.utilcodex)
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}